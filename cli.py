from dotenv import load_dotenv
load_dotenv()

import json
import os
import pandas as pd
from tqdm import tqdm
from src.handlers.openalex_handler import OpenAlexClient
from src.agents.disease_guardrail import validate_disease_name
from src.agents.article_screening import screen_abstracts_for_disease_epidemiology_generator
from src.handlers.pubmed_handler import download_fulltext_articles_generator
from src.agents.data_extraction import extract_data_from_article
from src.db import DatabaseConnection, ArticleManager, ExtractionManager
import logging

# Set up logging
logging.basicConfig(level=logging.ERROR, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

if __name__ == "__main__":
    # Initialize database connection
    db_connection = DatabaseConnection()
    infra_config = json.load(open("src/config.json"))['InfrastructureParameters']

    try:
        db_connection.connect()
        db_connection.create_tables()
        article_manager = ArticleManager(db_connection)
        extraction_manager = ExtractionManager(db_connection)
        print("✓ Database connection established and tables ready")
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
        print("Please check your database configuration in .env file or environment variables")
        print("See database_config.example for required settings")
        exit(1)

    disease_name = input("Enter the disease name (default: ulcerative colitis): ").strip()
    if not disease_name:
        disease_name = "ulcerative colitis"
    
    country = input(
        'Enter the two-letter ISO country code (default: US): '
    )
    if not country:
        country = "US"

    time_period = input(
        "Enter the time period in the format YYYY-YYYY (default: 1970-2025): "
    )
    if not time_period:
        time_period = "1970-2025"

    num_works = input("Enter the number of works (default: 50): ")
    if not num_works:
        num_works = 50
    else:
        num_works = int(num_works)

    print(f"### User Inputs:")
    print(f"- disease_name: {disease_name}")
    print(f"- country: {country}")
    print(f"- time_period: {time_period}")
    print(f"- num_works: {num_works}")

    # validate disease name and generate synonyms
    validation_result = validate_disease_name(disease_name)
    if validation_result["is_disease"]:
        disease_keywords = [disease_name]
    else:
        print("Invalid Disease Name: ", validation_result["explanation"])
        print("Suggested corrections: ", validation_result["suggested_corrections"])
        exit()
    

    # Check existing articles in database for this disease
    print(f"🔍 Checking existing articles for disease: {disease_name}")
    existing_count = article_manager.count_existing_articles_for_disease(disease_name)
    
    # Calculate how many new articles to search for
    remaining_to_search = max(0, num_works - existing_count)
    
    if remaining_to_search > 0:
        print(f"📚 Searching OpenAlex for {remaining_to_search} additional articles...")
        
        # built comprehensive query to search openalex for publications
        oaclient = OpenAlexClient(apply_topic_filtering=False)
        search_query = oaclient.construct_search_query(disease_keywords, country, time_period)
        publications = oaclient.search_works(
            search_query=search_query,
            country=country,
            time_period=time_period,
            num_works=remaining_to_search,
        )
        publications = pd.DataFrame(publications)
        publications['pdf_url'] = publications['pdf_url'].apply(
            lambda x: "" if pd.isna(x) else x
        )
        publications.set_index("pmid", inplace=True)
        
        # Save the article openalex records to MySQL database under table dim_articles
        print("💾 Saving new articles to database...")
        save_stats = article_manager.save_articles_from_openalex(publications, disease_name)
        print(f"✓ Articles saved: {save_stats['inserted']} inserted, {save_stats['updated']} updated, {save_stats['errors']} errors")
    else:
        print(f"✓ Already have {existing_count} articles for {disease_name}, no additional search needed")
        publications = pd.DataFrame()  # Empty dataframe for consistency

    # Check for articles needing screening
    print(f"🔍 Checking articles needing screening for disease: {disease_name}")
    articles_needing_screening = article_manager.get_articles_needing_screening(disease_name)
    
    if articles_needing_screening:
        print(f"📋 Found {len(articles_needing_screening)} articles needing screening...")
        
        # Prepare data for screening
        ids = [str(article.pmid) for article in articles_needing_screening]
        titles = [str(article.title) for article in articles_needing_screening]
        abstracts = [str(article.abstract or '') for article in articles_needing_screening]
        
        screening_results = []
        for result in tqdm(screen_abstracts_for_disease_epidemiology_generator(
            ids, disease_name, titles, abstracts, max_workers=infra_config['screening_workers'], time_period=time_period
        ), desc="Screening publications"):
            screening_results.append(result)
        
        # Update screening results in MySQL database under table dim_articles
        print("🔍 Updating screening results in database...")
        screening_stats = article_manager.update_screening_results(screening_results, disease_name)
        print(f"✓ Screening results updated: {screening_stats['updated']} updated, {screening_stats['not_found']} not found, {screening_stats['errors']} errors")
    else:
        print("✓ All articles already have screening results")

    # Get current statistics for this disease
    current_stats = article_manager.get_article_statistics()
    print(f"### Current statistics for {disease_name}:")
    for key, value in current_stats.items():
        print(f"- {key.replace('_', ' ').title()}: {value}")

    # Check for articles needing fulltext download
    print(f"📄 Checking articles needing fulltext download for disease: {disease_name}")
    articles_needing_fulltext = article_manager.get_articles_needing_fulltext(disease_name)
    
    if articles_needing_fulltext:
        print(f"📥 Found {len(articles_needing_fulltext)} articles needing fulltext download...")
        
        # Prepare PMCIDs for download
        pmcids_links = [str(article.pmcid) for article in articles_needing_fulltext if article.pmcid is not None]
        
        if pmcids_links:
            fulltext_results = []
            for result in tqdm(download_fulltext_articles_generator(pmcids_links, add_metadata=False, add_scanned_document=False, remove_irrelevant_sections=True, max_workers=infra_config['download_workers']), desc="Downloading full-text articles"):
                if result['status_code'] == 'SUCCESS':
                    pmcid = "PMC" + result['pmcid'].split("/")[-1]
                    fulltext_filepath = f"data/publications/{pmcid}.md"
                    with open(fulltext_filepath, "w", encoding="utf-8") as f:
                        f.write(result['fulltext_markdown'])
                    # Prepare result for database update
                    fulltext_results.append({
                        'pmcid': result['pmcid'],
                        'status_code': result['status_code'],
                        'fulltext_path': fulltext_filepath
                    })
                else:
                    # Prepare result for database update
                    fulltext_results.append({
                        'pmcid': result['pmcid'],
                        'status_code': result['status_code'],
                        'fulltext_path': None
                    })
            
            # Update fulltext status and path in MySQL database under table dim_articles
            print("📄 Updating fulltext status in database...")
            fulltext_stats = article_manager.update_fulltext_status(fulltext_results, disease_name)
            print(f"✓ Fulltext status updated: {fulltext_stats['updated']} updated, {fulltext_stats['not_found']} not found, {fulltext_stats['errors']} errors")
        else:
            print("⚠️ No valid PMCIDs found for fulltext download")
    else:
        print("✓ All relevant articles already have fulltext status")

    # Check for articles needing data extraction
    print(f"📊 Checking articles needing data extraction for disease: {disease_name}")
    articles_needing_extraction = article_manager.get_articles_needing_extraction(disease_name)
    
    if articles_needing_extraction:
        print(f"🔬 Found {len(articles_needing_extraction)} articles needing data extraction...")
        
        # Prepare data for extraction
        pmcids = [str(article.pmcid) for article in articles_needing_extraction if article.pmcid is not None]
        pmids = [str(article.pmid) for article in articles_needing_extraction if article.pmcid is not None]
        
        if pmcids and pmids:
            # Create pending extraction records in dim_extraction table
            print("📋 Creating pending extraction records...")
            pending_stats = extraction_manager.create_pending_extraction_records(pmids, pmcids)
            print(f"✓ Pending extraction records: {pending_stats['inserted']} created, {pending_stats['duplicates']} already exist, {pending_stats['errors']} errors")
            
            print(f"🔬 Extracting epidemiological data from {len(pmcids)} articles...")
            
            extraction_results = []
            all_extraction_stats = {
                'point_prevalence': {'inserted': 0, 'errors': 0},
                'period_prevalence': {'inserted': 0, 'errors': 0},
                'incidence_rate': {'inserted': 0, 'errors': 0},
                'cumulative_incidence': {'inserted': 0, 'errors': 0}
            }
            
            # Process extraction results - data is saved directly by extraction agents
            # Note: dim_extraction table is updated directly by each extraction agent
            for result in tqdm(extract_data_from_article(disease_name, pmcids, pmids, max_workers=infra_config['extraction_workers']), 
                             desc="Extracting epidemiological data"):
                extraction_results.append(result)
                
                # Track extraction results for statistics
                if result and result.get('status') == 'SUCCESS':
                    task_type = result.get('task', 'unknown').replace('_extraction', '')
                    if task_type in all_extraction_stats:
                        all_extraction_stats[task_type]['inserted'] += result.get('extracted_datapoints', 0)
                elif result and result.get('status') == 'FAILED':
                    task_type = result.get('task', 'unknown').replace('_extraction', '')
                    if task_type in all_extraction_stats:
                        all_extraction_stats[task_type]['errors'] += 1
                    logger.warning(f"Extraction failed for {result.get('PMID', 'unknown')}: {result.get('summary', 'No details')}")
            
            # Display extraction statistics
            print("📊 Data extraction completed:")
            total_inserted = sum(stats['inserted'] for stats in all_extraction_stats.values())
            total_errors = sum(stats['errors'] for stats in all_extraction_stats.values())
            
            for data_type, stats in all_extraction_stats.items():
                if stats['inserted'] > 0 or stats['errors'] > 0:
                    print(f"  - {data_type.replace('_', ' ').title()}: {stats['inserted']} inserted, {stats['errors']} errors")
            
            print(f"✓ Total extraction results: {total_inserted} records inserted, {total_errors} errors")
            
            # Display dim_extraction statistics
            dim_extraction_stats = extraction_manager.get_extraction_statistics_by_status()
            print(f"📊 Extraction tracking statistics:")
            print(f"  - Pending: {dim_extraction_stats['pending']}")
            print(f"  - Running: {dim_extraction_stats['running']}")
            print(f"  - Success: {dim_extraction_stats['success']}")
            print(f"  - Failed: {dim_extraction_stats['failed']}")
            print(f"  - Total tracked: {dim_extraction_stats['total']}")
        else:
            print("⚠️ No valid PMCIDs found for data extraction")
    else:
        print("✓ No articles available for data extraction (need relevant articles with successful fulltext)")

    # Display final database statistics
    print(f"\n### Final Database Statistics for {disease_name}:")
    final_stats = article_manager.get_article_statistics()
    for key, value in final_stats.items():
        print(f"- {key.replace('_', ' ').title()}: {value}")
    
    # Close database connection
    db_connection.close()
    print(f"\n✅ Processing completed successfully for {disease_name}!")