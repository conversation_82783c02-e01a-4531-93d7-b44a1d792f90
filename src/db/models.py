"""
Database models for the epidemiology research system.
"""

from typing import Optional
from datetime import datetime
from sqlalchemy import Column, String, Integer, Text, Boolean, DateTime, Float, JSON
from .connection import Base


class FactPointPrevalence(Base):
    """
    Fact table for point prevalence data extracted from research articles.
    """
    __tablename__ = "fact_point_prevalence"
    
    # Primary key - auto-generated
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # Link to source article
    pmid = Column(String(100), nullable=False, comment="Source article PMID")
    disease_name = Column(String(100), nullable=False, comment="Disease name from source article")
    pmcid = Column(String(100), nullable=False, comment="Source article PMCID")
    
    # Prevalence data (simplified)
    condition = Column(String(200), nullable=False, comment="Disease/condition name")
    point_prevalence_percent = Column(Float, nullable=False, comment="Point prevalence as percentage")
    year = Column(Integer, nullable=False, comment="Year of measurement")

    # Demographics (simplified - only country and gender)
    country = Column(String(100), nullable=True, comment="Country code or name")
    gender = Column(String(20), nullable=True, comment="Gender category")
    
    # Metadata
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)



class FactCumulativeIncidence(Base):
    """
    Fact table for cumulative incidence data extracted from research articles.
    """
    __tablename__ = "fact_cumulative_incidence"
    
    # Primary key - auto-generated
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # Link to source article
    pmid = Column(String(100), nullable=False, comment="Source article PMID")
    disease_name = Column(String(100), nullable=False, comment="Disease name from source article")
    pmcid = Column(String(100), nullable=False, comment="Source article PMCID")
    
    # Incidence data (simplified)
    condition = Column(String(200), nullable=False, comment="Disease/condition name")
    cumulative_incidence_percent = Column(Float, nullable=False, comment="Cumulative incidence as percentage")
    start_year = Column(Integer, nullable=False, comment="Start year of measurement")
    end_year = Column(Integer, nullable=False, comment="End year of measurement")

    # Demographics (simplified - only country and gender)
    country = Column(String(100), nullable=True, comment="Country code or name")
    gender = Column(String(20), nullable=True, comment="Gender category")
    
    # Metadata
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)


class DimArticles(Base):
    """
    Database model for storing research articles and their metadata.
    
    This table stores articles from OpenAlex with additional fields for
    screening results and full-text processing status.
    """
    __tablename__ = "dim_articles"
    
    # Primary identifiers
    pmid = Column(String(100), primary_key=True, nullable=False, 
                  comment="PubMed ID - primary identifier")
    disease_name = Column(String(100), primary_key=True, nullable=False,
                         comment="Disease name - primary identifier for tracking studies by disease")
    pmcid = Column(String(100), primary_key=True, nullable=False,
                   comment="PubMed Central ID - primary identifier")
    
    # Article metadata from OpenAlex
    title = Column(Text, nullable=False, comment="Article title")
    abstract = Column(Text, nullable=True, comment="Article abstract")
    publication_type = Column(String(50), nullable=True, 
                             comment="Type of publication (e.g., journal-article)")
    publication_year = Column(Integer, nullable=True, 
                             comment="Year of publication")
    pdf_url = Column(Text, nullable=True, 
                     comment="URL to PDF version of the article")
    
    # Screening results - added by disease epidemiology screening agent
    is_relevant = Column(Boolean, nullable=True, default=None,
                        comment="Whether article is relevant for disease epidemiology")
    
    # Full-text processing status - added by PubMed handler
    fulltext_status = Column(String(20), nullable=True, default=None,
                           comment="Status of full-text download (SUCCESS/FAILED)")
    fulltext_path = Column(Text, nullable=True, default=None,
                          comment="Local file path to downloaded full-text")
    
    # Metadata fields
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow,
                       comment="Timestamp when record was created")
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow,
                       onupdate=datetime.utcnow,
                       comment="Timestamp when record was last updated")
    
    def __repr__(self) -> str:
        return f"<DimArticles(pmid='{self.pmid}', title='{self.title[:50]}...')>"
    
    def to_dict(self) -> dict:
        """
        Convert model instance to dictionary.
        
        Returns:
            Dictionary representation of the article
        """
        return {
            "pmid": self.pmid,
            "disease_name": self.disease_name,
            "pmcid": self.pmcid,
            "title": self.title,
            "abstract": self.abstract,
            "publication_type": self.publication_type,
            "publication_year": self.publication_year,
            "pdf_url": self.pdf_url,
            "is_relevant": self.is_relevant,
            "fulltext_status": self.fulltext_status,
            "fulltext_path": self.fulltext_path,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
        }


class DimExtraction(Base):
    """
    Dimension table for tracking epidemiological data extraction status and results.
    
    This table tracks the extraction process for each article-task combination,
    storing the status (pending, running, success, failed) and extraction results.
    """
    __tablename__ = "dim_extraction"
    
    # Primary key - auto-generated
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # Link to source article (without disease_name as requested)
    pmid = Column(String(100), nullable=False, comment="Source article PMID")
    pmcid = Column(String(100), nullable=False, comment="Source article PMCID")
    
    # Extraction task information
    task_type = Column(String(50), nullable=False, 
                      comment="Type of extraction task (point_prevalence_extraction, period_prevalence_extraction, incidence_rate_extraction, cumulative_incidence_extraction)")
    
    # Extraction status and results
    extraction_status = Column(String(20), nullable=False, default="pending",
                              comment="Status of extraction (pending, running, success, failed)")
    extracted_datapoints = Column(Integer, nullable=True, default=0,
                                 comment="Number of datapoints successfully extracted")
    extraction_summary = Column(Text, nullable=True,
                               comment="Summary or error message from extraction process")
    
    # Full extraction response (for debugging and analysis)
    extraction_response = Column(JSON, nullable=True,
                                comment="Complete response object from extraction agent")
    
    # Timestamps
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow,
                       comment="Timestamp when extraction record was created")
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow,
                       onupdate=datetime.utcnow,
                       comment="Timestamp when extraction record was last updated")
    started_at = Column(DateTime, nullable=True,
                       comment="Timestamp when extraction process started")
    completed_at = Column(DateTime, nullable=True,
                         comment="Timestamp when extraction process completed")
    
    def __repr__(self) -> str:
        return f"<DimExtraction(pmid='{self.pmid}', task='{self.task_type}', status='{self.extraction_status}')>"
    
    def to_dict(self) -> dict:
        """
        Convert model instance to dictionary.
        
        Returns:
            Dictionary representation of the extraction record
        """
        return {
            "id": self.id,
            "pmid": self.pmid,
            "pmcid": self.pmcid,
            "task_type": self.task_type,
            "extraction_status": self.extraction_status,
            "extracted_datapoints": self.extracted_datapoints,
            "extraction_summary": self.extraction_summary,
            "extraction_response": self.extraction_response,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "started_at": self.started_at,
            "completed_at": self.completed_at,
        }
