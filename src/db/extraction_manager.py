"""
Epidemiological data extraction management operations for the epidemiology research system.
"""

from typing import List, Dict, Optional, Any, Union
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from datetime import datetime
import logging

from .connection import DatabaseConnection
from .models import (
    FactPointPrevalence,
    FactCumulativeIncidence,
    DimExtraction
)
from ..helpers.epidata_models import (
    PointPrevalenceRecord,
    CumulativeIncidenceRecord
)

logger = logging.getLogger(__name__)


class ExtractionManager:
    """
    Manages epidemiological data extraction operations in the database including CRUD operations
    for fact tables storing prevalence and incidence data.
    """
    
    def __init__(self, db_connection: DatabaseConnection):
        """
        Initialize ExtractionManager with database connection.
        
        Args:
            db_connection: DatabaseConnection instance
        """
        self.db_connection = db_connection
    
    def save_point_prevalence_records(
        self, 
        records: List[PointPrevalenceRecord], 
        pmid: str, 
        disease_name: str, 
        pmcid: str
    ) -> Dict[str, int]:
        """
        Save point prevalence records to the database.
        
        Args:
            records: List of PointPrevalenceRecord objects
            pmid: Source article PMID
            disease_name: Disease name from source article
            pmcid: Source article PMCID
        
        Returns:
            Dictionary with statistics: {"inserted": count, "errors": count}
        """
        session = self.db_connection.get_session()
        stats = {"inserted": 0, "errors": 0}
        
        try:
            for record in records:
                try:
                    fact_record = FactPointPrevalence(
                        pmid=pmid,
                        disease_name=disease_name,
                        pmcid=pmcid,
                        condition=record.condition,
                        point_prevalence_percent=record.point_prevalence_percent,
                        year=record.year,
                        country=record.country,
                        gender=record.gender
                    )
                    session.add(fact_record)
                    stats["inserted"] += 1
                    logger.debug(f"Inserted point prevalence record for {record.condition}")
                    
                except Exception as e:
                    stats["errors"] += 1
                    logger.error(f"Error processing point prevalence record: {e}")
                    continue
            
            session.commit()
            logger.info(f"Successfully saved point prevalence records: {stats}")
            
        except Exception as e:
            session.rollback()
            logger.error(f"Failed to save point prevalence records: {e}")
            raise
        finally:
            session.close()
        
        return stats
    


    def save_cumulative_incidence_records(
        self, 
        records: List[CumulativeIncidenceRecord], 
        pmid: str, 
        disease_name: str, 
        pmcid: str
    ) -> Dict[str, int]:
        """
        Save cumulative incidence records to the database.
        
        Args:
            records: List of CumulativeIncidenceRecord objects
            pmid: Source article PMID
            disease_name: Disease name from source article
            pmcid: Source article PMCID
        
        Returns:
            Dictionary with statistics: {"inserted": count, "errors": count}
        """
        session = self.db_connection.get_session()
        stats = {"inserted": 0, "errors": 0}
        
        try:
            for record in records:
                try:
                    fact_record = FactCumulativeIncidence(
                        pmid=pmid,
                        disease_name=disease_name,
                        pmcid=pmcid,
                        condition=record.condition,
                        cumulative_incidence_percent=record.cumulative_incidence_percent,
                        start_year=record.start_year,
                        end_year=record.end_year,
                        country=record.country,
                        gender=record.gender
                    )
                    session.add(fact_record)
                    stats["inserted"] += 1
                    logger.debug(f"Inserted cumulative incidence record for {record.condition}")
                    
                except Exception as e:
                    stats["errors"] += 1
                    logger.error(f"Error processing cumulative incidence record: {e}")
                    continue
            
            session.commit()
            logger.info(f"Successfully saved cumulative incidence records: {stats}")
            
        except Exception as e:
            session.rollback()
            logger.error(f"Failed to save cumulative incidence records: {e}")
            raise
        finally:
            session.close()
        
        return stats
    
    def save_epidemiological_data(
        self,
        extracted_data: Dict[str, List[Any]],
        pmid: str,
        disease_name: str,
        pmcid: str
    ) -> Dict[str, Dict[str, int]]:
        """
        Save all types of epidemiological data from extraction results.
        
        Args:
            extracted_data: Dictionary with keys 'point_prevalence', 'period_prevalence', 
                           'incidence_rate', 'cumulative_incidence' and their respective record lists
            pmid: Source article PMID
            disease_name: Disease name from source article
            pmcid: Source article PMCID
        
        Returns:
            Dictionary with statistics for each data type
        """
        results = {}
        
        # Save point prevalence records
        if 'point_prevalence' in extracted_data:
            results['point_prevalence'] = self.save_point_prevalence_records(
                extracted_data['point_prevalence'], pmid, disease_name, pmcid
            )
        

        # Save cumulative incidence records
        if 'cumulative_incidence' in extracted_data:
            results['cumulative_incidence'] = self.save_cumulative_incidence_records(
                extracted_data['cumulative_incidence'], pmid, disease_name, pmcid
            )
        
        logger.info(f"Successfully saved all epidemiological data for PMID {pmid}: {results}")
        return results
    
    def get_point_prevalence_by_disease(self, disease_name: str) -> List[FactPointPrevalence]:
        """
        Retrieve all point prevalence records for a specific disease.
        
        Args:
            disease_name: Disease name to filter by
        
        Returns:
            List of FactPointPrevalence objects
        """
        session = self.db_connection.get_session()
        
        try:
            records = session.query(FactPointPrevalence).filter(
                FactPointPrevalence.disease_name == disease_name
            ).all()
            
            logger.info(f"Retrieved {len(records)} point prevalence records for disease: {disease_name}")
            return records
            
        except Exception as e:
            logger.error(f"Failed to retrieve point prevalence records: {e}")
            raise
        finally:
            session.close()
    

    def get_cumulative_incidence_by_disease(self, disease_name: str) -> List[FactCumulativeIncidence]:
        """
        Retrieve all cumulative incidence records for a specific disease.
        
        Args:
            disease_name: Disease name to filter by
        
        Returns:
            List of FactCumulativeIncidence objects
        """
        session = self.db_connection.get_session()
        
        try:
            records = session.query(FactCumulativeIncidence).filter(
                FactCumulativeIncidence.disease_name == disease_name
            ).all()
            
            logger.info(f"Retrieved {len(records)} cumulative incidence records for disease: {disease_name}")
            return records
            
        except Exception as e:
            logger.error(f"Failed to retrieve cumulative incidence records: {e}")
            raise
        finally:
            session.close()
    
    def get_all_epidemiological_data_by_disease(self, disease_name: str) -> Dict[str, List[Any]]:
        """
        Retrieve all epidemiological data for a specific disease.
        
        Args:
            disease_name: Disease name to filter by
        
        Returns:
            Dictionary with all epidemiological data types
        """
        return {
            'point_prevalence': self.get_point_prevalence_by_disease(disease_name),
            'cumulative_incidence': self.get_cumulative_incidence_by_disease(disease_name)
        }
    
    def get_extraction_statistics(self, disease_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Get statistics about extracted epidemiological data.
        
        Args:
            disease_name: Optional disease name to filter by
        
        Returns:
            Dictionary with various statistics
        """
        session = self.db_connection.get_session()
        
        try:
            stats = {}
            
            # Base queries (simplified)
            point_prev_query = session.query(FactPointPrevalence)
            cumulative_inc_query = session.query(FactCumulativeIncidence)

            # Filter by disease if specified
            if disease_name:
                point_prev_query = point_prev_query.filter(FactPointPrevalence.disease_name == disease_name)
                cumulative_inc_query = cumulative_inc_query.filter(FactCumulativeIncidence.disease_name == disease_name)

            stats = {
                "point_prevalence_records": point_prev_query.count(),
                "cumulative_incidence_records": cumulative_inc_query.count()
            }
            
            stats["total_records"] = sum(stats.values())
            
            logger.info(f"Extraction statistics{' for ' + disease_name if disease_name else ''}: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get extraction statistics: {e}")
            raise
        finally:
            session.close()
    
    def delete_epidemiological_data_by_article(self, pmid: str, disease_name: str, pmcid: str) -> Dict[str, int]:
        """
        Delete all epidemiological data for a specific article.
        
        Args:
            pmid: Source article PMID
            disease_name: Disease name from source article
            pmcid: Source article PMCID
        
        Returns:
            Dictionary with deletion statistics
        """
        session = self.db_connection.get_session()
        stats = {"deleted": 0}
        
        try:
            # Delete from simplified fact tables
            deleted_point = session.query(FactPointPrevalence).filter(
                FactPointPrevalence.pmid == pmid,
                FactPointPrevalence.disease_name == disease_name,
                FactPointPrevalence.pmcid == pmcid
            ).delete()

            deleted_cumulative = session.query(FactCumulativeIncidence).filter(
                FactCumulativeIncidence.pmid == pmid,
                FactCumulativeIncidence.disease_name == disease_name,
                FactCumulativeIncidence.pmcid == pmcid
            ).delete()

            stats["deleted"] = deleted_point + deleted_cumulative
            
            session.commit()
            logger.info(f"Deleted {stats['deleted']} epidemiological records for article {pmid}")
            
        except Exception as e:
            session.rollback()
            logger.error(f"Failed to delete epidemiological data: {e}")
            raise
        finally:
            session.close()
        
        return stats

    # Dim Extraction Management Methods
    
    def create_pending_extraction_records(
        self, 
        pmids: List[str], 
        pmcids: List[str]
    ) -> Dict[str, int]:
        """
        Create pending extraction records for articles that need data extraction.
        
        Args:
            pmids: List of PubMed IDs
            pmcids: List of PubMed Central IDs
        
        Returns:
            Dictionary with statistics: {"inserted": count, "errors": count, "duplicates": count}
        """
        session = self.db_connection.get_session()
        stats = {"inserted": 0, "errors": 0, "duplicates": 0}
        
        # Task types for extraction
        task_types = [
            "point_prevalence_extraction",
            "period_prevalence_extraction", 
            "incidence_rate_extraction",
            "cumulative_incidence_extraction"
        ]
        
        try:
            for pmid, pmcid in zip(pmids, pmcids):
                for task_type in task_types:
                    try:
                        # Check if record already exists
                        existing = session.query(DimExtraction).filter(
                            DimExtraction.pmid == pmid,
                            DimExtraction.pmcid == pmcid,
                            DimExtraction.task_type == task_type
                        ).first()
                        
                        if existing:
                            stats["duplicates"] += 1
                            continue
                            
                        extraction_record = DimExtraction(
                            pmid=pmid,
                            pmcid=pmcid,
                            task_type=task_type,
                            extraction_status="pending",
                            extracted_datapoints=0
                        )
                        session.add(extraction_record)
                        stats["inserted"] += 1
                        logger.debug(f"Created pending extraction record for PMID {pmid}, task {task_type}")
                        
                    except Exception as e:
                        stats["errors"] += 1
                        logger.error(f"Error creating extraction record for PMID {pmid}, task {task_type}: {e}")
                        continue
            
            session.commit()
            logger.info(f"Successfully created pending extraction records: {stats}")
            
        except Exception as e:
            session.rollback()
            logger.error(f"Failed to create pending extraction records: {e}")
            raise
        finally:
            session.close()
        
        return stats
    
    def update_extraction_status(
        self,
        pmid: str,
        pmcid: str,
        task_type: str,
        status: str,
        extracted_datapoints: Optional[int] = None,
        summary: Optional[str] = None,
        response_data: Optional[dict] = None
    ) -> bool:
        """
        Update extraction status for a specific article-task combination.
        
        Args:
            pmid: PubMed ID
            pmcid: PubMed Central ID
            task_type: Type of extraction task
            status: New status (running, success, failed)
            extracted_datapoints: Number of datapoints extracted
            summary: Summary or error message
            response_data: Complete response object from extraction agent
        
        Returns:
            True if update was successful, False otherwise
        """
        session = self.db_connection.get_session()
        
        try:
            extraction_record = session.query(DimExtraction).filter(
                DimExtraction.pmid == pmid,
                DimExtraction.pmcid == pmcid,
                DimExtraction.task_type == task_type
            ).first()
            
            if not extraction_record:
                logger.warning(f"No extraction record found for PMID {pmid}, task {task_type}")
                return False
            
            # Update fields
            extraction_record.extraction_status = status  # type: ignore
            if extracted_datapoints is not None:
                extraction_record.extracted_datapoints = extracted_datapoints  # type: ignore
            if summary is not None:
                extraction_record.extraction_summary = summary  # type: ignore
            if response_data is not None:
                extraction_record.extraction_response = response_data  # type: ignore
            
            # Update timestamps based on status
            if status == "running" and extraction_record.started_at is None:
                extraction_record.started_at = datetime.utcnow()  # type: ignore
            elif status in ["success", "failed"]:
                extraction_record.completed_at = datetime.utcnow()  # type: ignore
            
            session.commit()
            logger.debug(f"Updated extraction status for PMID {pmid}, task {task_type}: {status}")
            return True
            
        except Exception as e:
            session.rollback()
            logger.error(f"Failed to update extraction status: {e}")
            return False
        finally:
            session.close()
    
    def update_extraction_from_response(
        self,
        extraction_response: Dict[str, Any]
    ) -> bool:
        """
        Update extraction record from extraction agent response.
        
        Args:
            extraction_response: Response object from extraction agent containing:
                - PMID, PMCID, task, status, extracted_datapoints, summary
        
        Returns:
            True if update was successful, False otherwise
        """
        try:
            pmid = str(extraction_response.get("PMID", ""))
            pmcid = str(extraction_response.get("PMCID", ""))
            task_type = str(extraction_response.get("task", ""))
            status = str(extraction_response.get("status", "")).lower()
            extracted_datapoints = int(extraction_response.get("extracted_datapoints", 0))
            summary = str(extraction_response.get("summary", ""))
            
            if not all([pmid, pmcid, task_type]):
                logger.error(f"Invalid extraction response: missing required fields")
                return False
            
            return self.update_extraction_status(
                pmid=pmid,
                pmcid=pmcid,
                task_type=task_type,
                status=status,
                extracted_datapoints=extracted_datapoints,
                summary=summary,
                response_data=extraction_response
            )
            
        except Exception as e:
            logger.error(f"Failed to update extraction from response: {e}")
            return False
    
    def get_pending_extractions(self) -> List[DimExtraction]:
        """
        Get all extraction records with pending status.
        
        Returns:
            List of DimExtraction objects with pending status
        """
        session = self.db_connection.get_session()
        
        try:
            records = session.query(DimExtraction).filter(
                DimExtraction.extraction_status == "pending"
            ).all()
            
            logger.info(f"Retrieved {len(records)} pending extraction records")
            return records
            
        except Exception as e:
            logger.error(f"Failed to retrieve pending extractions: {e}")
            raise
        finally:
            session.close()
    
    def get_extraction_statistics_by_status(
        self, 
        pmid: Optional[str] = None,
        pmcid: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get extraction statistics grouped by status.
        
        Args:
            pmid: Optional filter by PMID
            pmcid: Optional filter by PMCID
        
        Returns:
            Dictionary with extraction statistics by status
        """
        session = self.db_connection.get_session()
        
        try:
            query = session.query(DimExtraction)
            
            if pmid:
                query = query.filter(DimExtraction.pmid == pmid)
            if pmcid:
                query = query.filter(DimExtraction.pmcid == pmcid)
            
            records = query.all()
            
            stats = {
                "pending": 0,
                "running": 0, 
                "success": 0,
                "failed": 0,
                "total": len(records),
                "total_datapoints": 0
            }
            
            for record in records:
                status = str(record.extraction_status)
                if status in stats:
                    stats[status] += 1
                datapoints = getattr(record, 'extracted_datapoints', None)
                if datapoints is not None and datapoints > 0:
                    stats["total_datapoints"] += int(datapoints)
            
            logger.info(f"Extraction statistics: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get extraction statistics: {e}")
            raise
        finally:
            session.close()
    
    def get_extraction_records_by_article(
        self, 
        pmid: str, 
        pmcid: str
    ) -> List[DimExtraction]:
        """
        Get all extraction records for a specific article.
        
        Args:
            pmid: PubMed ID
            pmcid: PubMed Central ID
        
        Returns:
            List of DimExtraction objects for the article
        """
        session = self.db_connection.get_session()
        
        try:
            records = session.query(DimExtraction).filter(
                DimExtraction.pmid == pmid,
                DimExtraction.pmcid == pmcid
            ).all()
            
            logger.info(f"Retrieved {len(records)} extraction records for article PMID {pmid}")
            return records
            
        except Exception as e:
            logger.error(f"Failed to retrieve extraction records for article: {e}")
            raise
        finally:
            session.close()
    
    def delete_extraction_records_by_article(
        self, 
        pmid: str, 
        pmcid: str
    ) -> Dict[str, int]:
        """
        Delete all extraction records for a specific article.
        
        Args:
            pmid: PubMed ID
            pmcid: PubMed Central ID
        
        Returns:
            Dictionary with deletion statistics
        """
        session = self.db_connection.get_session()
        stats = {"deleted": 0}
        
        try:
            deleted_count = session.query(DimExtraction).filter(
                DimExtraction.pmid == pmid,
                DimExtraction.pmcid == pmcid
            ).delete()
            
            stats["deleted"] = deleted_count
            session.commit()
            logger.info(f"Deleted {deleted_count} extraction records for article PMID {pmid}")
            
        except Exception as e:
            session.rollback()
            logger.error(f"Failed to delete extraction records: {e}")
            raise
        finally:
            session.close()
        
        return stats
