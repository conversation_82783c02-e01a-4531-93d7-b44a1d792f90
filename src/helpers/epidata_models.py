from typing import Optional, Literal
from pydantic import BaseModel, Field

# -----------------------------
# 1) Point Prevalence (at a single year)
# -----------------------------
class PointPrevalenceRecord(BaseModel):
    """Simplified point prevalence record with only essential fields."""
    condition: str = Field(..., description="Short free-form name for the disease/condition. e.g., 'Type 2 diabetes'.")
    point_prevalence_percent: float = Field(..., ge=0, le=100,
        description=("Point prevalence as PERCENT in range [0,100] at the specified year. Computed as (n_cases / n_population) * 100. Report upto 1 decimal place precision.")
    )
    year: int = Field(..., ge=1800, le=2100, description="Calendar year (YYYY) of measurement (e.g., 2021).")

    # Demographics / location (simplified)
    country: Optional[str] = Field(
        None,
        description=("Country (single field). Prefer ISO 3166-1 alpha-2 code like 'IN'. If unavailable, use full country name.")
    )
    gender: Optional[Literal["male", "female", "both", "unknown"]] = Field("both", description="Gender category for this estimate.")


# -----------------------------
# 4) Cumulative Incidence (Risk over a year range)
# -----------------------------
class CumulativeIncidenceRecord(BaseModel):
    """Simplified cumulative incidence record with only essential fields."""
    condition: str = Field(..., description="Disease/condition (free text).")
    cumulative_incidence_percent: float = Field(
        ..., ge=0, le=100,
        description=("Cumulative incidence (RISK) as a PERCENT in [0,100] over the specified year range. Compute as (n_new_cases / n_at_risk_start) * 100. This is a proportion over time (not per PY).")
    )
    start_year: int = Field(..., ge=1800, le=2100, description="Start year (YYYY) of measurement.")
    end_year: int = Field(..., ge=1800, le=2100, description="End year (YYYY) of measurement.")

    # Demographics / location (simplified)
    country: Optional[str] = Field(
        None,
        description=("Country (single field). Prefer ISO 3166-1 alpha-2 code like 'IN'. If unavailable, use full country name.")
    )
    gender: Optional[Literal["male", "female", "both", "other", "unknown"]] = Field("both", description="Gender category for this estimate.")

# 5) Summary
class ExtractionSummary(BaseModel):
    identified_datapoints: int = Field(..., ge=0, description="Number of datapoints identified in the article.")
    extracted_datapoints: int = Field(..., ge=0, description="Number of datapoints successfully extracted and saved into database from the article.")