from dotenv import load_dotenv
load_dotenv()

# Add project root to Python path for imports
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Any, Generator, Dict, List
from src.agents.point_prevalence_data_extraction import extract_point_prevalence_from_article
from src.agents.cumulative_incidence_data_extraction import extract_cumulative_incidence_from_article

def extract_data_from_article(disease_name: str, pmcids: List[str], pmids: List[str], max_workers: int = 20) -> Generator[Dict[str, Any], None, None]:
    """
    Extract epidemiological data from an article using simplified agents (point prevalence and cumulative incidence only).
    """
    futures = []
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures.extend([
            executor.submit(
                extract_point_prevalence_from_article, disease_name, pmcid, pmid
            ) for pmcid, pmid in zip(pmcids, pmids)
        ])
        futures.extend([
            executor.submit(
                extract_cumulative_incidence_from_article, disease_name, pmcid, pmid
            ) for pmcid, pmid in zip(pmcids, pmids)
        ])
        for future in as_completed(futures):
            result = future.result()
            yield result

if __name__ == "__main__":
    disease_name = "Diabetes Mellitus"
    pmcids = ["PMC4426991", "PMC5536674"]
    pmids = ["12345678", "30930098"]
    for result in extract_data_from_article(disease_name, pmcids, pmids):
        print("--------------------------------")
        print(result)
        print("--------------------------------")