from dotenv import load_dotenv
load_dotenv()

import random
from textwrap import dedent
from typing import Dict, List, Generator, Optional
from agno.agent import Agent
from agno.models.openai import OpenAIChat
import json
import os
from pydantic import BaseModel
from agno.tools.reasoning import ReasoningTools
from concurrent.futures import ThreadPoolExecutor, as_completed

from pydantic import BaseModel


class DiseaseEpidemiologyScreeningResult(BaseModel):
    """Result model for disease-specific epidemiology abstract screening.

    Attributes:
        is_relevant (bool): Whether the title and abstract represents a simple epidemiology study with clear prevalence and/or incidence data for the given disease.
    """

    is_relevant: bool



class DiseaseAbstractScreeningAgent:
    """
    Agent for screening scientific abstracts to determine if they are disease epidemiology studies
    for a specific disease.
    """

    def __init__(self) -> None:
        """
        Initialize the Disease Abstract Screening Agent.
        """
        path = os.path.dirname(os.path.abspath(__file__))
        with open(os.path.join(path, "..", "config.json")) as f:
            config = json.load(f)["DiseaseAbstractScreeningAgent"]

        self.screening_agent = Agent(
            model=OpenAIChat(id=config["model_id"]),
            instructions=self._get_screening_instructions(),
            tools=[ReasoningTools()] if config["use_reasoning"] else None,
            retries=config["retries"],
            delay_between_retries=random.randint(1,3),
            exponential_backoff=True,
            output_schema=DiseaseEpidemiologyScreeningResult,
            debug_mode=config["debug_mode"],
            telemetry=False
        )

    def _get_screening_instructions(self) -> str:
        """Get detailed instructions for disease-specific abstract screening with stricter filtering criteria."""
        return dedent(
            """
            You are an expert epidemiologist and medical researcher. Your task is to analyze scientific titles and abstracts to evaluate whether they represent epidemiology studies for the specified disease that meet STRICT filtering criteria for high-quality, country-level epidemiological data.

            IS_RELEVANT: Does the title and abstract represent an epidemiology study for the specified disease that meets ALL strict filtering criteria? (true/false)

            A study qualifies as RELEVANT ONLY if it meets ALL of the following STRICT criteria:

            **MANDATORY REQUIREMENTS (ALL must be met):**
            1. **SINGLE DISEASE FOCUS**: The specified disease is the ONLY primary focus of the study. Studies examining multiple diseases or conditions are EXCLUDED.

            2. **COUNTRY-LEVEL DATA**: The study must report country-level (national) prevalence and/or incidence data. Studies reporting only regional, city-level, facility-level, or sub-national data are EXCLUDED.

            3. **NO ETHNICITY STRATIFICATION**: The study must NOT include ethnicity-based stratification, subgroup analysis, or ethnic group comparisons. Studies focusing on specific ethnic groups are EXCLUDED.

            4. **NO AGE STRATIFICATION**: The study must NOT include age-based stratification, age group analysis, or age-specific breakdowns. Studies focusing on specific age groups (pediatric, elderly, etc.) are EXCLUDED.

            5. **GENDER STRATIFICATION ACCEPTABLE**: Studies may include gender stratification (male/female comparisons) as some diseases are gender-specific.

            6. **EPIDEMIOLOGICAL OUTCOMES**: Must report at least one of: point prevalence, cumulative incidence, incidence rate, or prevalence rate at the country level.

            7. **TIME PERIOD COMPLIANCE**: The study data must fall within a reasonable epidemiological time frame (typically within the last 25 years unless historical analysis is specified).

            **PREFERRED STUDY DESIGNS (Must use one):**
            - National cross-sectional surveys
            - Country-wide population-based studies
            - National disease registries
            - National surveillance systems
            - Government health surveys (national level)
            - National epidemiological surveillance reports
            - Country-wide cohort studies reporting incidence

            **AUTOMATICALLY EXCLUDE:**
            - Systematic reviews and meta-analyses
            - Mendelian randomization studies
            - In vitro and animal studies
            - Clinical trials and intervention studies
            - Case reports and case series
            - Multi-disease or comorbidity studies
            - Regional, city, or facility-specific studies
            - Studies with ethnic or racial stratification
            - Studies with age group stratification
            - Hospital-based studies without national scope
            - Modeling studies without real population data
            - Risk factor or biomarker studies
            - Diagnostic accuracy studies

            **GEOGRAPHIC SCOPE REQUIREMENTS:**
            - Must explicitly state country-level or national scope
            - Must NOT be limited to specific regions, cities, or healthcare facilities
            - Must NOT focus on specific ethnic communities or populations
            - Must represent the general population of the entire country

            **DATA QUALITY REQUIREMENTS:**
            - Clear case definitions for the specified disease
            - Defined study population and denominators
            - Standard epidemiological methodology
            - Transparent data collection methods
            - Representative sampling of national population

            **TITLE AND ABSTRACT ANALYSIS:**
            - Title must clearly indicate national/country-level scope
            - Abstract must describe country-wide methodology
            - Look for terms like "national", "country-wide", "population-based"
            - Verify absence of terms indicating stratification by ethnicity or age
            - Confirm the specified disease is the sole focus
            - Check for appropriate epidemiological outcome measures

            CRITICAL: Only accept studies that provide clean, country-level epidemiological data for a single disease without demographic stratification (except gender). The goal is to obtain high-quality, nationally representative data that can be compared across countries and time periods.

            Be STRICT in your evaluation - when in doubt, EXCLUDE the study.
            """
        )

    def screen_abstract(
        self, disease: str, title: str, abstract: str, time_period: Optional[str] = None
    ) -> Dict[str, bool]:
        """
        Screen a title and abstract to determine if it's an epidemiology study for a specific disease
        that meets strict filtering criteria.

        Args:
            disease (str): The disease name to check for in the title and abstract.
            title (str): The scientific article title to analyze.
            abstract (str): The scientific abstract text to analyze.
            time_period (str, optional): Time period range (e.g., "2000-2025") for validation.

        Returns:
            Dict[str, bool]: Dictionary containing screening result:
                - is_relevant
        """
        if not abstract or not abstract.strip() or not disease or not disease.strip():
            return {
                "is_relevant": False,
            }

        # Handle missing title gracefully
        title_text = title.strip() if title else "No title provided"

        # Prepare time period context
        time_context = ""
        if time_period:
            time_context = f"\n\nTIME PERIOD CONTEXT:\nUser is searching for studies with data from {time_period}. Ensure the study data falls within a reasonable epidemiological timeframe."

        prompt = f"""
        Analyze the following scientific title and abstract to determine if it represents an epidemiology study for the specified disease that meets ALL strict filtering criteria.

        DISEASE NAME:
        {disease}

        TITLE:
        {title_text}

        ABSTRACT:
        {abstract}{time_context}

        Apply the STRICT filtering criteria:
        1. Single disease focus (ONLY the specified disease)
        2. Country-level (national) data scope
        3. NO ethnicity-based stratification or subgroup analysis
        4. NO age-based stratification or age group analysis
        5. Gender stratification is acceptable
        6. Must report country-level prevalence and/or incidence data
        7. Must use appropriate epidemiological study design
        8. Must provide data within reasonable time period

        Be STRICT - when in doubt, EXCLUDE the study.
        """

        try:
            response = self.screening_agent.run(prompt, disease=disease)
            if isinstance(response.content, DiseaseEpidemiologyScreeningResult):
                return response.content.model_dump()
            else:
                print(f"Screening Error: {response}\n\n type:{type(response)}")
                return {
                    "is_relevant": False,
                }
        except Exception as e:
            print(f"Exception during screening: {e}")
            return {
                "is_relevant": False,
            }


def screen_abstract_for_disease_epidemiology(
    disease: str, title: str, abstract: str, time_period: Optional[str] = None
) -> Dict[str, bool]:
    """
    Convenience function to screen a single title and abstract for a specific disease.

    Args:
        disease (str): The disease name to check for in the title and abstract.
        title (str): The article title to analyze.
        abstract (str): The abstract text to analyze.
        time_period (str, optional): Time period range for validation.

    Returns:
        Dict[str, bool]: Dictionary containing screening result.
    """
    agent = DiseaseAbstractScreeningAgent()
    return agent.screen_abstract(disease, title, abstract, time_period)


def screen_abstracts_for_disease_epidemiology(
    disease: str, titles: List[str], abstracts: List[str], max_workers: int = 5, time_period: Optional[str] = None
) -> List[Dict[str, bool]]:
    """
    Convenience function to screen multiple titles and abstracts for a specific disease.

    Args:
        disease (str): The disease name to check for in the titles and abstracts.
        titles (List[str]): The article titles to analyze.
        abstracts (List[str]): The abstract texts to analyze.
        max_workers (int): Maximum number of worker threads for parallel processing.
        time_period (str, optional): Time period range for validation.

    Returns:
        List[Dict[str, bool]]: List of screening results for each title/abstract pair.
    """

    if len(titles) != len(abstracts):
        raise ValueError("Number of titles must match number of abstracts")

    results = [{}] * len(abstracts)  # Pre-allocate list to maintain order

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks with their indices
        future_to_index = {
            executor.submit(screen_abstract_for_disease_epidemiology, disease, title, abstract, time_period): i
            for i, (title, abstract) in enumerate(zip(titles, abstracts))
        }

        # Collect results as they complete
        for future in as_completed(future_to_index):
            index = future_to_index[future]
            result = future.result()
            results[index] = result
    return results

def screen_abstracts_for_disease_epidemiology_generator(
    ids: List[str], disease: str, titles: List[str], abstracts: List[str], max_workers: int = 5, time_period: Optional[str] = None
) -> Generator[Dict[str, bool], None, None]:
    """
    Convenience function to screen multiple titles and abstracts for a specific disease.

    Args:
        ids (List[str]): The article IDs to analyze.
        disease (str): The disease name to check for in the titles and abstracts.
        titles (List[str]): The article titles to analyze.
        abstracts (List[str]): The abstract texts to analyze.
        max_workers (int): Maximum number of worker threads for parallel processing.
        time_period (str, optional): Time period range for validation.

    Returns:
        Generator[Dict[str, bool], None, None]: Generator of screening results for each title/abstract pair.
    """

    if len(titles) != len(abstracts):
        raise ValueError("Number of titles must match number of abstracts")

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks with their titles and abstracts
        future_to_index = {
            executor.submit(screen_abstract_for_disease_epidemiology, disease, title, abstract, time_period): (id, title, abstract)
            for id, title, abstract in zip(ids, titles, abstracts)
        }

        # Collect results as they complete
        for future in as_completed(future_to_index):
            id, title, abstract = future_to_index[future]
            result = future.result()
            final_result = {
                "id": id,
                "title": title, 
                "abstract": abstract,
                "is_relevant": result["is_relevant"]
            }
            yield final_result

if __name__ == "__main__":
    print("Testing Disease-Specific Abstract Screening Agent...")

    # Test with a sample epidemiology abstract for Type 2 diabetes
    disease = "Type 2 diabetes mellitus"
    epidemiology_title = "Prevalence and incidence of Type 2 diabetes mellitus in urban adults: a population-based cross-sectional study, 2018-2022"
    epidemiology_abstract = """
    Objective: To estimate the prevalence and incidence of Type 2 diabetes mellitus in the adult population of Urban City from 2018-2022. Methods: We conducted a population-based cross-sectional study using electronic health records from 50 primary care clinics. The study included 125,000 adults aged 18-65 years. Diabetes prevalence was calculated for each year, and incidence rates were determined for new diagnoses. Results: The overall prevalence of Type 2 diabetes increased from 8.2% (95% CI: 7.9-8.5%) in 2018 to 9.7% (95% CI: 9.4-10.0%) in 2022. The annual incidence rate was 12.5 per 1,000 person-years (95% CI: 11.8-13.2). Higher incidence was observed in males and individuals aged 45-65 years. Conclusion: Type 2 diabetes prevalence and incidence continue to rise in this urban population, highlighting the need for enhanced prevention strategies.
    """

    print("\n=== Testing with Epidemiology Abstract for Type 2 diabetes mellitus ===")
    result = screen_abstract_for_disease_epidemiology(disease, epidemiology_title, epidemiology_abstract)

    print(f"Is Relevant Study: {result['is_relevant']}")

    # Test with a non-epidemiology abstract for rheumatoid arthritis
    disease2 = "rheumatoid arthritis"
    clinical_trial_title = "Efficacy and safety of Drug X versus placebo in rheumatoid arthritis: a randomized controlled trial"
    clinical_trial_abstract = """
    Background: This randomized controlled trial evaluated the efficacy of Drug X versus placebo 
    in treating patients with rheumatoid arthritis. Methods: 200 patients were randomized to receive 
    either Drug X (n=100) or placebo (n=100) for 12 weeks. Primary outcome was reduction in 
    Disease Activity Score. Results: Drug X group showed significantly greater improvement in 
    disease activity compared to placebo (p<0.001). Side effects were mild and manageable. 
    Conclusion: Drug X is effective and safe for treating rheumatoid arthritis.
    """

    print("\n\n=== Testing with Clinical Trial Abstract for rheumatoid arthritis ===")
    result2 = screen_abstract_for_disease_epidemiology(disease2, clinical_trial_title, clinical_trial_abstract)

    print(f"Is Relevant Study: {result2['is_relevant']}")

    # Test with multiple abstracts using parallel processing
    disease3 = "hypertension"
    multiple_titles = [
        "Prevalence of hypertension in rural Bangladesh: a cross-sectional population-based survey",
        "Effectiveness of a new antihypertensive medication in resistant hypertension: a randomized trial",
        "Ten-year incidence of hypertension in middle-aged adults: a prospective cohort study"
    ]
    multiple_abstracts = [
        """
        Objective: To determine the prevalence of hypertension in adults aged 25-64 years in rural Bangladesh. 
        Methods: A cross-sectional population-based survey was conducted among 2,500 adults. Blood pressure 
        measurements were taken using standardized protocols. Results: The overall prevalence of hypertension 
        was 28.4% (95% CI: 26.7-30.1%). Prevalence increased with age and was higher in males. 
        Conclusion: Hypertension prevalence is substantial in rural Bangladesh.
        """,
        """
        Background: This study evaluated the effectiveness of a new antihypertensive medication in 
        patients with resistant hypertension. Methods: 150 patients were enrolled in a double-blind 
        randomized trial. Primary endpoint was reduction in systolic blood pressure after 12 weeks. 
        Results: The new medication showed significant blood pressure reduction compared to placebo 
        (p<0.001). Conclusion: The new medication is effective for treating resistant hypertension.
        """,
        """
        Objective: To assess the 10-year incidence of hypertension in a cohort of middle-aged adults. 
        Methods: A prospective cohort study followed 5,000 normotensive adults aged 40-60 years from 
        2010-2020. Annual health screenings documented new hypertension diagnoses. Results: The cumulative 
        incidence of hypertension was 35.2% over 10 years. Incidence rate was 42.1 per 1,000 person-years. 
        Conclusion: Hypertension incidence remains high in this population.
        """
    ]

    print("\n\n=== Testing with Multiple Abstracts for hypertension (Parallel Processing) ===")
    results = screen_abstracts_for_disease_epidemiology(disease3, multiple_titles, multiple_abstracts, max_workers=3)

    for i, result in enumerate(results):
        print(f"\nAbstract {i+1}:")
        print(f"  Title: {multiple_titles[i]}")
        print(f"  Is Relevant Study: {result['is_relevant']}")
