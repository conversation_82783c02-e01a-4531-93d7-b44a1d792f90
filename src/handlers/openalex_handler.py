# Add project root to Python path for imports
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import os
from typing import Literal, Union, Optional
import json, requests
from unstructured.cleaners.core import replace_unicode_quotes
import pycountry

class OpenAlexClient:
    def __init__(self, apply_topic_filtering:bool=False):
        self.apply_topic_filtering = apply_topic_filtering

        # Create epidemiology topic names set for O(1) lookup 
        self.topics = json.load(open(os.path.join(project_root, "data", "openalex_interest_topics.json")))
        self.topic_names = {
            topic.get("display_name") for topic in self.topics 
            if topic.get("display_name", None) is not None
        }

        # load search query terms
        self.search_query_terms = json.load(open(os.path.join(project_root, "data", "openalex_query_terms.json")))
        self.search_suffix_query = ""
        for key, terms in self.search_query_terms.items():
            if key == "NOT":
                self.search_suffix_query += " NOT (\"" + "\" OR \"".join(terms) + "\")"
            elif key.startswith("AND_") and self.search_suffix_query=="":
                self.search_suffix_query = "(\"" + "\" OR \"".join(terms) + "\")"
            elif key.startswith("AND_") and self.search_suffix_query!="":
                self.search_suffix_query += " AND (\"" + "\" OR \"".join(terms) + "\")"
            else:
                raise ValueError(f"Invalid key: {key}")

    def _process_identifiers(
        self,
        work: dict
    ) -> dict:
        """
        Process the identifiers of a work
        """
        identifiers = {}
        if work.get("ids", {}).get("pmid", None) is not None:
            identifiers["pmid"] = work.get("ids", {}).get("pmid")
        if work.get("ids", {}).get("pmcid", None) is not None:
            identifiers["pmcid"] = work.get("ids", {}).get("pmcid")
        return identifiers
    
    def _publication_access_information(
        self,
        work: dict
    ) -> dict:
        """
        Parse the publication access information from the work object
        """
        access_information = {}
        if work.get("primary_location", {}).get("pdf_url", None) is not None:
            access_information["pdf_url"] = work.get("primary_location", {}).get("pdf_url")
        if work.get("primary_location", {}).get("type", None) is not None:
            access_information["publication_type"] = work.get("primary_location", {}).get("type")
        if work.get("publication_year", None) is not None:
            access_information["publication_year"] = int(work.get("publication_year", -1))
        return access_information

    def _construct_abstract(
        self,
        work: dict
    ) -> Union[str, None]:
        """
        Construct the publication abstract of a work using abstract_inverted_index 
        """
        if len(work.get("abstract_inverted_index", {})) > 3:
            raw_abstract = work.get("abstract_inverted_index", {})
        else:
            return None
        
        # Create a list to store (position, word) tuples
        position_word_pairs = []
        for word, positions in raw_abstract.items():
            for position in positions:
                position_word_pairs.append((position, word))
        
        # Sort by position (first element of tuple) and extract words
        position_word_pairs.sort(key=lambda x: x[0])
        abstract = " ".join(word for _, word in position_word_pairs)
        abstract = replace_unicode_quotes(abstract)
        return abstract
    
    def _parse_institute_countries(
        self,
        work: dict
    ) -> list:
        """
        Parse the countries of the institutes from work object
        """
        # Use set from the beginning instead of converting list to set later
        country_codes = set()
        for authorship in work.get("authorships", []):
            if authorship.get("institutions", []):
                for institution in authorship.get("institutions", []):
                    if institution.get("country_code", None) is not None:
                        country_codes.add(institution.get("country_code"))
        return list(country_codes)
    
    def _filter_works(
        self,
        works: list
    ) -> list:
        """
        Filter and process the works based on the interested topics, and abstract availability. Each work contains keys

        Args:
            works (list): A list of works.

        Returns:
            filtered_works (list): A list of filtered works.
        """
        filtered_works = []
        for work in works:
            abstract = self._construct_abstract(work)
            if abstract is None:
                continue

            if self.apply_topic_filtering:
                if not any( topic['display_name'] in self.topic_names for topic in work.get("topics", []) ):
                    continue

            modified_work = self._process_identifiers(work)
            modified_work.update(self._publication_access_information(work))
            modified_work["title"] = str(work.get("title", ""))
            modified_work["abstract"] = str(abstract)
            # modified_work["institute_countries"] = self._parse_institute_countries(work)
            filtered_works.append(modified_work)
        return filtered_works

    def search_works(
        self,
        search_query: str,
        country: str,
        time_period: str = "2000-2025",
        num_works: int = 100
    ) -> list:
        """
        Search the work entities in OpenAlex database using search terms, published country and published time period

        Args:
            search_query (str): A openalex format query with boolean operators "OR", "AND" and "NOT" with phrase enclosed in double quotes.
            country (str): The two-letter ISO country code of the country.
            time_period (str): The time period in which the works are published in the format YYYY-YYYY.
            num_works (int): Limit the number of works after sorting them by relevance score.

        Returns:
            works (list): A list of works that match the criteria. Each work contains keys:
                - pmid (str): The PMID of the work.
                - pmcid (str): The PMCID of the work.
                - publication_type (str): The type of the publication.
                - publication_year (int): The publication year of the work.
                - title (str): The title of the work.
                - abstract (str): The abstract of the work.
                - pdf_url : The URL of the PDF of the publication.
        """
        base_url = "https://api.openalex.org/works"
        works = []

        # Combine keywords into a single search query using logical OR
        text_query = f"title_and_abstract.search:{search_query}"
        from_year, to_year = time_period.split("-")
        from_publication_date = f"{from_year}-01-01"
        to_publication_date = f"{to_year}-12-31"
        country_code = f"institutions.country_code:{country}"
        publication_period = f"from_publication_date:{from_publication_date},to_publication_date:{to_publication_date}"
        contains_abstract = "has_abstract:true"
        contains_pmcid = "has_pmcid:true"
        is_open_accessible = "has_oa_accepted_or_published_version:true"
        is_retracted = "is_retracted:false"
        language_identifier = "language:en"

        selected_fields = [
            "ids",
            "title",
            "publication_year",
            "abstract_inverted_index",
            "primary_location",
            "authorships",
            "topics"
        ]

        filter_query = ",".join([
            text_query,
            country_code, 
            publication_period, 
            contains_abstract, 
            contains_pmcid, 
            is_open_accessible,
            is_retracted,
            language_identifier
        ])

        # initial API call in pagination
        select_query = ",".join(selected_fields)
        params = {
            "filter": filter_query,
            "select": select_query,
            "per_page": 200,
            "sort": "relevance_score:desc",
            "cursor": "*"
        }
        response = requests.get(base_url, params=params, timeout=30)
        response.raise_for_status()
        
        # Store parsed JSON to avoid redundant parsing
        response_data = response.json()
        retrieved_works = response_data.get("results", [])
        filtered_retrieved_works = self._filter_works(retrieved_works)
        works.extend(filtered_retrieved_works)

        total_number_of_works = min(response_data.get("meta", {}).get("count", 0), num_works)
        # print(f"Total number of works: {total_number_of_works}\n API query: {params}")
        params["cursor"] = response_data.get("meta", {}).get("next_cursor", None)
        
        # Early termination: stop when we reach num_works or no more cursor
        while len(works) < total_number_of_works and len(works) < num_works and params["cursor"]:
            response = requests.get(base_url, params=params, timeout=30)
            response.raise_for_status()
            response_data = response.json()
            retrieved_works = response_data.get("results", [])
            filtered_retrieved_works = self._filter_works(retrieved_works)
            works.extend(filtered_retrieved_works)
            params["cursor"] = response_data.get("meta", {}).get("next_cursor", None)
            
        # Return only the requested number of works
        return works[:num_works]
    
    def construct_search_query(
        self,
        disease_keywords: list,
        country: Optional[str] = None,
        time_period: Optional[str] = None
    ) -> str:
        """
        Construct an enhanced disease epidemiology study search query for the OpenAlex API
        with dynamic query term generation based on time period and country details.

        Args:
            disease_keywords (list): List of disease-related keywords
            country (str, optional): Two-letter ISO country code for enhanced country terms
            time_period (str, optional): Time period in format "YYYY-YYYY" for year-specific terms

        Returns:
            str: Enhanced OpenAlex search query with improved precision
        """
        # Base disease query
        disease_query = "(\"" + "\" OR \"".join(disease_keywords) + "\")"

        # Enhanced epidemiological terms with more specific focus
        enhanced_epi_terms = [
            "prevalence", "incidence", "epidemiology", "surveillance",
            "population-based", "national survey", "cross-sectional",
            "disease burden", "public health", "registry-based"
        ]
        epi_query = "(\"" + "\" OR \"".join(enhanced_epi_terms) + "\")"

        # Dynamic time period terms
        time_query = ""
        if time_period and "-" in time_period:
            try:
                start_year, end_year = time_period.split("-")
                start_year, end_year = int(start_year), int(end_year)
                # Add individual years from the range for better matching
                years = [str(year) for year in range(start_year, min(end_year + 1, start_year + 10))]  # Limit to 10 years max
                if years:
                    time_query = " AND (\"" + "\" OR \"".join(years) + "\")"
            except (ValueError, TypeError):
                pass  # Skip if time_period format is invalid

        # Enhanced country terms using pycountry
        country_query = ""
        if country:
            country_terms = [country.upper()]  # ISO code
            try:
                country_obj = pycountry.countries.get(alpha_2=country.upper())
                if country_obj:
                    country_terms.append(country_obj.name)  # Full country name
                    # Add common alternative names
                    if hasattr(country_obj, 'common_name'):
                        country_terms.append(country_obj.common_name)
                    if hasattr(country_obj, 'official_name'):
                        country_terms.append(country_obj.official_name)
            except:
                pass  # Skip if country lookup fails

            if country_terms:  # Add country terms if we have any
                country_query = " AND (\"" + "\" OR \"".join(country_terms) + "\")"

        # Enhanced exclusion terms for better precision
        enhanced_exclusions = [
            "systematic review", "meta-analysis", "scoping review",
            "mendelian randomization", "in vitro", "murine", "mouse", "rat", "animal",
            "case report", "case series", "clinical trial", "randomized controlled trial",
            "intervention study", "treatment efficacy", "drug therapy", "pharmacological",
            "genetic association", "genome-wide", "biomarker", "molecular", "cellular",
            "in silico", "computational model", "mathematical model", "simulation"
        ]
        exclusion_query = " NOT (\"" + "\" OR \"".join(enhanced_exclusions) + "\")"

        # Combine all parts with proper Boolean logic
        final_query = disease_query + " AND " + epi_query + time_query + country_query + exclusion_query

        return final_query
    
if __name__ == "__main__":
    oaclient = OpenAlexClient()
    query = '(\"ulcerative colitis\") AND (\"prevalence\" OR \"incidence\")'
    works = oaclient.search_works(
        search_query=query,
        country="US",
        time_period="2000-2025",
        num_works=10
    )
    print(len(works))
    for work in works:
        print("Title: ", work["title"])
        print("Abstract: ", work["abstract"][:50])
        print("="*100)