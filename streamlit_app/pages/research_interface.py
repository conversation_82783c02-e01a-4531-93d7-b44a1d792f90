"""
Epidemiology Research Interface Page

This page replicates the functionality of the CLI interface with an interactive
user experience and real-time progress tracking for long-running operations.
"""

# Add project root to Python path for imports
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import streamlit as st
import json
import pandas as pd
from typing import Dict, Any, Optional
import time
from datetime import datetime
import os
import threading
import logging
import pycountry

logger = logging.getLogger(__name__)

# Import business logic components
from src.handlers.openalex_handler import OpenAlexClient
from src.agents.disease_guardrail import validate_disease_name
from src.agents.article_screening import screen_abstracts_for_disease_epidemiology_generator
from src.handlers.pubmed_handler import download_fulltext_articles_generator
from src.agents.data_extraction import extract_data_from_article

# Import Streamlit utilities
from streamlit_app.utils.session_state import (
    update_research_progress,
    update_operation_status,
    set_research_running,
    set_research_completed,
    add_research_error,
    reset_research_progress
)
from streamlit_app.utils.database import get_article_manager_for_thread, get_extraction_manager_for_thread
from streamlit_app.utils.progress_manager import get_progress_manager, sync_progress_to_session_state
from streamlit_app.components.progress_tracker import ProgressTracker

def get_country_options():
    """Get a list of country options for the dropdown."""
    countries = [""]  # Add empty option for no selection
    try:
        for country in pycountry.countries:
            # Add both the country name and code for easy selection
            countries.append(f"{country.name} ({country.alpha_2})")
        return sorted(countries)
    except Exception as e:
        # Fallback to common countries if pycountry fails
        return ["", "United States (US)", "United Kingdom (GB)", "Canada (CA)", "Australia (AU)", "Germany (DE)", "France (FR)", "Japan (JP)", "China (CN)", "India (IN)"]

def extract_country_code(country_selection: str) -> str:
    """Extract the country code from the dropdown selection."""
    if not country_selection:
        return ""
    # Extract the code from "Country Name (CODE)" format
    if "(" in country_selection and ")" in country_selection:
        return country_selection.split("(")[-1].replace(")", "")
    return country_selection

def research_interface_page():
    """Main research interface page."""

    st.title("🔍 Epidemiology Research Interface")
    st.markdown("Conduct epidemiology research with real-time progress tracking")

    # Sync progress from background thread to session state with robust error handling
    sync_success = sync_progress_to_session_state(st.session_state)
    if not sync_success:
        logger.warning("Progress sync returned False - potential data corruption")

    # Check if research is currently running with robust state checking
    is_running = st.session_state.research_progress.get("is_running", False)

    # Additional validation - check if progress manager also thinks research is running
    manager_is_running = False
    progress_manager = get_progress_manager()
    manager_progress = progress_manager.get_progress()
    manager_is_running = manager_progress.get("is_running", False)

    # If there's a mismatch, trust the progress manager and force a sync
    if is_running != manager_is_running:
        logger.warning(f"Session state mismatch detected: session={is_running}, manager={manager_is_running}")
        is_running = manager_is_running
    
        sync_success = sync_progress_to_session_state(st.session_state)
        if sync_success:
            logger.info("Successfully synced progress after mismatch")
        else:
            logger.error("Failed to sync progress after mismatch")
    

    # Final validation - if sync failed but manager says running, trust the manager
    if not sync_success and manager_is_running:
        logger.warning("Sync failed but manager indicates research is running - preserving progress view")
        is_running = True

    # Additional safeguard - check for recent errors that might indicate false completion
    if not is_running and st.session_state.research_progress.get("errors"):
        recent_errors = st.session_state.research_progress.get("errors", [])
        non_critical_indicators = ["Non-critical error", "File error (continuing)", "continuing"]

        # If the last few errors are non-critical, the research might still be running
        if recent_errors and any(indicator in str(recent_errors[-1]) for indicator in non_critical_indicators):
            logger.warning("Research marked as stopped but recent errors are non-critical - checking manager")
            progress_manager = get_progress_manager()
            manager_progress = progress_manager.get_progress()
            if manager_progress.get("is_running", False):
                logger.info("Manager confirms research is still running despite non-critical errors")
                is_running = True
                # Force sync to update session state
                sync_progress_to_session_state(st.session_state)

    # CRITICAL FIX: Always trust the progress manager over session state
    # This prevents the UI from reverting to the form due to session state corruption
    if manager_is_running and not is_running:
        logger.warning("CRITICAL: Manager says running but session state says stopped - forcing progress view")
        is_running = True
        sync_progress_to_session_state(st.session_state)

    # Debug information (can be removed later)
    if is_running:
        st.info(f"Research Running: {st.session_state.research_progress.get('current_step', 'Unknown')}")
    else:
        st.info("Research Idle")

    # ALWAYS show progress view if manager thinks research is running
    if is_running or manager_is_running:
        display_research_progress()
        return

    # Research form (only if both session state and manager agree research is not running)
    display_research_form()

def display_research_form():
    """Display the research configuration form."""
    
    st.markdown("### Research Configuration")
    
    with st.form("research_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            disease_name = st.text_input(
                "Disease Name",
                value=st.session_state.research_form_data.get("disease_name", ""),
                help="Enter the disease name to research (e.g., 'ulcerative colitis')"
            )

            # Country dropdown with pycountry integration
            country_options = get_country_options()
            current_country = st.session_state.research_form_data.get("country", "")

            # Find the current selection in the dropdown format
            current_selection = ""
            if current_country:
                for option in country_options:
                    if current_country.upper() in option:
                        current_selection = option
                        break

            country_selection = st.selectbox(
                "Country",
                options=country_options,
                index=country_options.index(current_selection) if current_selection in country_options else 0,
                help="Select the country for research focus"
            )
        
        with col2:
            time_period = st.text_input(
                "Time Period",
                value=st.session_state.research_form_data.get("time_period", ""),
                help="Enter the time period in YYYY-YYYY format (e.g., '1970-2025')"
            )

            num_works = st.number_input(
                "Number of Works",
                min_value=1,
                max_value=1000,
                value=st.session_state.research_form_data.get("num_works", 50),
                help="Number of research articles to analyze"
            )

        # Record limit filter
        st.markdown("### Data Extraction Limits")
        col_limit1, col_limit2 = st.columns(2)

        with col_limit1:
            max_records_per_article = st.number_input(
                "Max Records per Article",
                min_value=1,
                max_value=100,
                value=st.session_state.research_form_data.get("max_records_per_article", 10),
                help="Maximum number of data records to extract per article"
            )

        with col_limit2:
            max_total_records = st.number_input(
                "Max Total Records",
                min_value=1,
                max_value=10000,
                value=st.session_state.research_form_data.get("max_total_records", 1000),
                help="Maximum total number of data records to extract across all articles"
            )
        
        # Advanced options
        with st.expander("Advanced Options"):
            st.markdown("**Worker Configuration**")
            col3, col4, col5 = st.columns(3)
            
            with col3:
                screening_workers = st.number_input(
                    "Screening Workers",
                    min_value=1,
                    max_value=20,
                    value=5,
                    help="Number of parallel workers for article screening"
                )
            
            with col4:
                download_workers = st.number_input(
                    "Download Workers",
                    min_value=1,
                    max_value=20,
                    value=10,
                    help="Number of parallel workers for fulltext download"
                )
            
            with col5:
                extraction_workers = st.number_input(
                    "Extraction Workers",
                    min_value=1,
                    max_value=20,
                    value=20,
                    help="Number of parallel workers for data extraction"
                )
        
        submitted = st.form_submit_button("🚀 Start Research", type="primary")
        
        if submitted:
            # Validate inputs
            if not disease_name or not disease_name.strip():
                st.error("Please enter a disease name")
                return

            # Extract country code from selection
            country_code = extract_country_code(country_selection)
            if not country_code:
                st.error("Please select a country")
                return

            # Update form data in session state
            st.session_state.research_form_data.update({
                "disease_name": disease_name.strip(),
                "country": country_code.upper(),
                "time_period": time_period.strip() if time_period else "",
                "num_works": num_works,
                "max_records_per_article": max_records_per_article,
                "max_total_records": max_total_records
            })

            # Start research process
            start_research_process(
                disease_name.strip(),
                country_code.upper(),
                time_period.strip() if time_period else "",
                num_works,
                screening_workers,
                download_workers,
                extraction_workers,
                max_records_per_article,
                max_total_records
            )

def start_research_process(
    disease_name: str,
    country: str,
    time_period: str,
    num_works: int,
    screening_workers: int,
    download_workers: int,
    extraction_workers: int,
    max_records_per_article: int = 10,
    max_total_records: int = 1000
):
    """Start the research process in a background thread."""

    # Reset progress and set as running
    reset_research_progress()
    set_research_running(disease_name, country, total_steps=6)

    # Store worker configuration
    st.session_state.worker_config = {
        "screening_workers": screening_workers,
        "download_workers": download_workers,
        "extraction_workers": extraction_workers
    }

    # Initialize progress manager
    progress_manager = get_progress_manager()
    progress_manager.reset_progress()
    progress_manager.set_running(disease_name, country, total_steps=6)

    # Start research in background thread
    research_thread = threading.Thread(
        target=run_research_pipeline_thread_safe,
        args=(disease_name, country, time_period, num_works),
        daemon=True
    )
    research_thread.start()

    # Rerun to show progress screen
    st.rerun()

def display_research_progress():
    """Display real-time research progress with streaming updates."""

    # Create persistent containers for streaming updates
    if 'progress_containers' not in st.session_state:
        st.session_state.progress_containers = {}

    st.markdown("### 🔄 Research in Progress")

    # Create containers for different sections
    metrics_container = st.container()
    progress_container = st.container()
    status_container = st.container()
    operations_container = st.container()
    results_container = st.container()
    errors_container = st.container()
    controls_container = st.container()

    # Store containers for streaming updates
    st.session_state.progress_containers.update({
        'metrics': metrics_container,
        'progress': progress_container,
        'status': status_container,
        'operations': operations_container,
        'results': results_container,
        'errors': errors_container,
        'controls': controls_container
    })

    # Use streaming progress display
    display_streaming_progress()

def display_streaming_progress():
    """Display progress using streaming updates from progress manager."""

    progress_manager = get_progress_manager()
    progress_info = progress_manager.get_progress()

    # Don't do redundant running checks here - let the main logic handle it
    # This prevents race conditions that cause the UI to revert to the form

    containers = st.session_state.get('progress_containers', {})

    # Update metrics
    if 'metrics' in containers:
        with containers['metrics']:
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Disease", progress_info.get("disease_name", "Unknown"))
            with col2:
                st.metric("Country", progress_info.get("country", "Unknown"))
            with col3:
                completed = progress_info.get("completed_steps", 0)
                total = progress_info.get("total_steps", 6)
                st.metric("Progress", f"{completed}/{total}")

    # Update progress bar
    if 'progress' in containers:
        with containers['progress']:
            total_steps = max(progress_info.get("total_steps", 1), 1)
            completed_steps = progress_info.get("completed_steps", 0)
            overall_progress = completed_steps / total_steps
            st.progress(overall_progress, text=f"Overall Progress: {overall_progress:.1%}")

    # Update current step
    if 'status' in containers:
        with containers['status']:
            current_step = progress_info.get("current_step", "Unknown")
            st.info(f"**Current Step:** {current_step}")

    # Update operation progress
    if 'operations' in containers:
        with containers['operations']:
            st.markdown("### 📈 Operation Progress")
            operation_status = progress_manager.get_all_operation_status()
            for operation, status in operation_status.items():
                if status.get("total", 0) > 0:
                    progress = status.get("completed", 0) / status.get("total", 1)
                    st.progress(progress, text=f"{operation.title()}: {status.get('completed', 0)}/{status.get('total', 0)}")

    # Update results
    if 'results' in containers:
        with containers['results']:
            results = progress_info.get("results", {})
            if results and isinstance(results, dict):
                st.markdown("### 📊 Results")
                for step, result in results.items():
                    with st.expander(f"Results: {step}"):
                        st.json(result)

    # Update errors
    if 'errors' in containers:
        with containers['errors']:
            errors = progress_info.get("errors", [])
            if errors and isinstance(errors, list):
                st.markdown("### ⚠️ Errors")
                for error in errors:
                    # Only show critical errors to avoid cluttering the UI
                    if "Critical error" in str(error):
                        st.error(str(error))
                    elif "Non-critical error" in str(error):
                        st.warning(str(error))

    # Update controls
    if 'controls' in containers:
        with containers['controls']:
            if st.button("🛑 Stop Research", type="secondary"):
                progress_manager.set_completed()
                st.warning("Research stopped by user")
                st.rerun()


# Auto-refresh for streaming progress updates

    progress_manager = get_progress_manager()
    manager_progress = progress_manager.get_progress()

    if manager_progress.get("is_running", False):
        # Use a controlled refresh interval for streaming updates
        import time

        current_time = time.time()

        # Initialize refresh timing
        if not hasattr(st.session_state, 'last_refresh_time'):
            st.session_state.last_refresh_time = current_time

        time_since_last_refresh = current_time - st.session_state.last_refresh_time

        # Refresh every 2 seconds for better stability
        if time_since_last_refresh >= 2.0:
            st.session_state.last_refresh_time = current_time
            time.sleep(0.2)  # Small delay for thread synchronization
            st.rerun()
        else:
            # Wait for the remaining time
            remaining_time = 2.0 - time_since_last_refresh
            time.sleep(remaining_time)
            st.rerun()

def run_research_pipeline(disease_name: str, country: str, time_period: str, num_works: int):
    """Run the complete research pipeline (executed in background thread)."""

    # Get progress manager for thread-safe communication
    progress_manager = get_progress_manager()

    # Load infrastructure config
    with open("src/config.json", "r") as f:
        infra_config = json.load(f)["InfrastructureParameters"]

    # Get worker config from session state (this was set before thread started)
    # We'll use default values if not available
    worker_config = {
        "screening_workers": 5,
        "download_workers": 10,
        "extraction_workers": 20
    }
    infra_config.update(worker_config)

    # Get database managers (thread-safe versions)
    article_manager = get_article_manager_for_thread()
    extraction_manager = get_extraction_manager_for_thread()

    if not article_manager or not extraction_manager:
        progress_manager.add_error("Failed to initialize database managers")
        progress_manager.set_completed()
        return

    # Step 1: Validate disease name
    progress_manager.update_progress("Validating disease name...", increment_completed=True)
    validation_result = validate_disease_name(disease_name)

    if not validation_result["is_disease"]:
        add_research_error(f"Invalid disease name: {validation_result['explanation']}")
        set_research_completed()
        return

    disease_keywords = [disease_name]

    # Step 2: Check existing articles and search for new ones
    update_research_progress("Searching for articles...", increment_completed=True)

    existing_count = article_manager.count_existing_articles_for_disease(disease_name)
    remaining_to_search = max(0, num_works - existing_count)

    if remaining_to_search > 0:
        # Search OpenAlex for new articles
        oaclient = OpenAlexClient(apply_topic_filtering=False)
        search_query = oaclient.construct_search_query(disease_keywords, country, time_period)
        publications = oaclient.search_works(
            search_query=search_query,
            country=country,
            time_period=time_period,
            num_works=remaining_to_search,
        )

        publications_df = pd.DataFrame(publications)
        publications_df['pdf_url'] = publications_df['pdf_url'].apply(
            lambda x: "" if pd.isna(x) else x
        )
        publications_df.set_index("pmid", inplace=True)

        # Save articles to database
        save_stats = article_manager.save_articles_from_openalex(publications_df, disease_name)
        # Store results in session state
        if "results" not in st.session_state.research_progress:
            st.session_state.research_progress["results"] = {}
        st.session_state.research_progress["results"]["article_search"] = save_stats
    else:
        # Store results in session state
        if "results" not in st.session_state.research_progress:
            st.session_state.research_progress["results"] = {}
        st.session_state.research_progress["results"]["article_search"] = {
            "message": f"Already have {existing_count} articles for {disease_name}"
        }

    # Step 3: Screen articles for epidemiological relevance
    update_research_progress("Screening articles for relevance...", increment_completed=True)
    articles_needing_screening = article_manager.get_articles_needing_screening(disease_name)

    if articles_needing_screening:
        update_operation_status("screening", total=len(articles_needing_screening), completed=0, status="running")

        # Prepare data for screening
        ids = [str(article.pmid) for article in articles_needing_screening]
        titles = [str(article.title) for article in articles_needing_screening]
        abstracts = [str(article.abstract or '') for article in articles_needing_screening]

        screening_results = []
        completed_screening = 0

        for result in screen_abstracts_for_disease_epidemiology_generator(
            ids, disease_name, titles, abstracts, max_workers=infra_config.get('screening_workers', 5), time_period=time_period
        ):
            screening_results.append(result)
            completed_screening += 1
            update_operation_status("screening", completed=completed_screening)

        # Update screening results in database
        screening_stats = article_manager.update_screening_results(screening_results, disease_name)
        # Store results in session state
        if "results" not in st.session_state.research_progress:
            st.session_state.research_progress["results"] = {}
        st.session_state.research_progress["results"]["screening"] = screening_stats
        update_operation_status("screening", status="completed")
    else:
        # Store results in session state
        if "results" not in st.session_state.research_progress:
            st.session_state.research_progress["results"] = {}
        st.session_state.research_progress["results"]["screening"] = {
            "message": "All articles already have screening results"
        }

    # Continue with remaining steps in next part...
    _continue_research_pipeline(disease_name, article_manager, extraction_manager, infra_config)

def _continue_research_pipeline(disease_name: str, article_manager, extraction_manager, infra_config: dict):
    """Continue the research pipeline with fulltext download and data extraction."""

    # Step 4: Download fulltext articles
    update_research_progress("Downloading fulltext articles...", increment_completed=True)
    articles_needing_fulltext = article_manager.get_articles_needing_fulltext(disease_name)

    if articles_needing_fulltext:
        pmcids_links = [str(article.pmcid) for article in articles_needing_fulltext if article.pmcid is not None]

        if pmcids_links:
            update_operation_status("download", total=len(pmcids_links), completed=0, status="running")

            fulltext_results = []
            completed_downloads = 0

            for result in download_fulltext_articles_generator(
                pmcids_links,
                add_metadata=False,
                add_scanned_document=False,
                remove_irrelevant_sections=True,
                max_workers=infra_config.get('download_workers', 10)
            ):
                if result['status_code'] == 'SUCCESS':
                    pmcid = "PMC" + result['pmcid'].split("/")[-1]
                    fulltext_filepath = f"{project_root}/data/publications/{pmcid}.md"

                    # Ensure directory exists
                    import os
                    os.makedirs(f"{project_root}/data/publications", exist_ok=True)

                    with open(fulltext_filepath, "w", encoding="utf-8") as f:
                        f.write(result['fulltext_markdown'])

                    fulltext_results.append({
                        'pmcid': result['pmcid'],
                        'status_code': result['status_code'],
                        'fulltext_path': fulltext_filepath
                    })
                else:
                    fulltext_results.append({
                        'pmcid': result['pmcid'],
                        'status_code': result['status_code'],
                        'fulltext_path': None
                    })

                completed_downloads += 1
                update_operation_status("download", completed=completed_downloads)

            # Update fulltext status in database
            fulltext_stats = article_manager.update_fulltext_status(fulltext_results, disease_name)
            st.session_state.research_progress["results"]["fulltext"] = fulltext_stats
            update_operation_status("download", status="completed")
        else:
            st.session_state.research_progress["results"]["fulltext"] = {
                "message": "No valid PMCIDs found for fulltext download"
            }
    else:
        st.session_state.research_progress["results"]["fulltext"] = {
            "message": "All relevant articles already have fulltext status"
        }

    # Step 5: Extract epidemiological data
    update_research_progress("Extracting epidemiological data...", increment_completed=True)
    articles_needing_extraction = article_manager.get_articles_needing_extraction(disease_name)

    if articles_needing_extraction:
        pmcids = [str(article.pmcid) for article in articles_needing_extraction if article.pmcid is not None]
        pmids = [str(article.pmid) for article in articles_needing_extraction if article.pmcid is not None]

        if pmcids and pmids:
            # Create pending extraction records
            pending_stats = extraction_manager.create_pending_extraction_records(pmids, pmcids)

            # Calculate total extraction tasks (4 types per article)
            total_extractions = len(pmcids) * 4
            update_operation_status("extraction", total=total_extractions, completed=0, status="running")

            extraction_results = []
            completed_extractions = 0
            all_extraction_stats = {
                'point_prevalence': {'inserted': 0, 'errors': 0},
                'period_prevalence': {'inserted': 0, 'errors': 0},
                'incidence_rate': {'inserted': 0, 'errors': 0},
                'cumulative_incidence': {'inserted': 0, 'errors': 0}
            }

            for result in extract_data_from_article(
                disease_name, pmcids, pmids, max_workers=infra_config.get('extraction_workers', 20)
            ):
                extraction_results.append(result)
                completed_extractions += 1
                update_operation_status("extraction", completed=completed_extractions)

                # Track extraction results for statistics
                if result and result.get('status') == 'SUCCESS':
                    task_type = result.get('task', 'unknown').replace('_extraction', '')
                    if task_type in all_extraction_stats:
                        all_extraction_stats[task_type]['inserted'] += result.get('extracted_datapoints', 0)
                elif result and result.get('status') == 'FAILED':
                    task_type = result.get('task', 'unknown').replace('_extraction', '')
                    if task_type in all_extraction_stats:
                        all_extraction_stats[task_type]['errors'] += 1

            st.session_state.research_progress["results"]["extraction"] = all_extraction_stats
            update_operation_status("extraction", status="completed")
        else:
            st.session_state.research_progress["results"]["extraction"] = {
                "message": "No valid PMCIDs found for data extraction"
            }
    else:
        st.session_state.research_progress["results"]["extraction"] = {
            "message": "No articles available for data extraction"
        }

    # Step 6: Finalize and display statistics
    update_research_progress("Finalizing results...", increment_completed=True)
    final_stats = article_manager.get_article_statistics()
    st.session_state.research_progress["results"]["final_stats"] = final_stats
    set_research_completed()


def run_research_pipeline_thread_safe(disease_name: str, country: str, time_period: str, num_works: int):
    """Thread-safe version of the research pipeline that uses progress manager."""

    # Get progress manager for thread-safe communication
    progress_manager = get_progress_manager()

    logger.info(f"Starting research pipeline: {disease_name}, {country}, {time_period}, {num_works} works")

    # Load infrastructure config
    with open("src/config.json", "r") as f:
        infra_config = json.load(f)["InfrastructureParameters"]

    # Use default worker config (can be enhanced later)
    worker_config = {
        "screening_workers": 5,
        "download_workers": 10,
        "extraction_workers": 20
    }
    infra_config.update(worker_config)

    # Get database managers (thread-safe versions)
    article_manager = get_article_manager_for_thread()
    extraction_manager = get_extraction_manager_for_thread()

    if not article_manager or not extraction_manager:
        progress_manager.add_error("Failed to initialize database managers")
        progress_manager.set_completed()
        return

    # Step 1: Validate disease name
    progress_manager.update_progress("Validating disease name...", increment_completed=True)
    validation_result = validate_disease_name(disease_name)

    if not validation_result["is_disease"]:
        progress_manager.add_error(f"Invalid disease name: {validation_result['explanation']}")
        progress_manager.set_completed()
        return

    disease_keywords = [disease_name]

    # Step 2: Check existing articles and search for new ones
    progress_manager.update_progress("Searching for articles...", increment_completed=True)

    existing_count = article_manager.count_existing_articles_for_disease(disease_name)
    remaining_to_search = max(0, num_works - existing_count)

    if remaining_to_search > 0:
        # Search OpenAlex for new articles
        oaclient = OpenAlexClient(apply_topic_filtering=False)
        search_query = oaclient.construct_search_query(disease_keywords, country, time_period)
        publications = oaclient.search_works(
            search_query=search_query,
            country=country,
            time_period=time_period,
            num_works=remaining_to_search,
        )

        publications_df = pd.DataFrame(publications)
        publications_df['pdf_url'] = publications_df['pdf_url'].apply(
            lambda x: "" if pd.isna(x) else x
        )
        publications_df.set_index("pmid", inplace=True)

        # Save articles to database
        save_stats = article_manager.save_articles_from_openalex(publications_df, disease_name)
        progress_manager.add_result("article_search", save_stats)
    else:
        progress_manager.add_result("article_search", {
            "message": f"Already have {existing_count} articles for {disease_name}"
        })

    # Step 3: Screen articles for epidemiological relevance
    progress_manager.update_progress("Screening articles for relevance...", increment_completed=True)
    articles_needing_screening = article_manager.get_articles_needing_screening(disease_name)

    if articles_needing_screening:
        progress_manager.update_operation_status("screening", total=len(articles_needing_screening), completed=0, status="running")

        # Prepare data for screening
        ids = [str(article.pmid) for article in articles_needing_screening]
        titles = [str(article.title) for article in articles_needing_screening]
        abstracts = [str(article.abstract or '') for article in articles_needing_screening]

        screening_results = []
        completed_screening = 0

        for result in screen_abstracts_for_disease_epidemiology_generator(
            ids, disease_name, titles, abstracts, max_workers=infra_config.get('screening_workers', 5), time_period=time_period
        ):
            screening_results.append(result)
            completed_screening += 1
            progress_manager.update_operation_status("screening", completed=completed_screening)

        # Update screening results in database
        screening_stats = article_manager.update_screening_results(screening_results, disease_name)
        progress_manager.add_result("screening", screening_stats)
        progress_manager.update_operation_status("screening", status="completed")
    else:
        progress_manager.add_result("screening", {
            "message": "All articles already have screening results"
        })

    # Continue with remaining steps...
    _continue_research_pipeline_thread_safe(disease_name, country, time_period, article_manager, extraction_manager, infra_config, progress_manager)

def _continue_research_pipeline_thread_safe(disease_name: str, country: str, time_period: str, article_manager, extraction_manager, infra_config: dict, progress_manager):
    """Continue the research pipeline with fulltext download and data extraction (thread-safe)."""

    # Step 4: Download fulltext articles
    progress_manager.update_progress("Downloading fulltext articles...", increment_completed=True)
    articles_needing_fulltext = article_manager.get_articles_needing_fulltext(disease_name)

    if articles_needing_fulltext:
        pmcids_links = [str(article.pmcid) for article in articles_needing_fulltext if article.pmcid is not None]

        if pmcids_links:
            progress_manager.update_operation_status("download", total=len(pmcids_links), completed=0, status="running")

            fulltext_results = []
            completed_downloads = 0

            for result in download_fulltext_articles_generator(pmcids_links, max_workers=infra_config.get('download_workers', 10)):
                if result['status_code'] == 'SUCCESS':
                    pmcid = "PMC" + result['pmcid'].split("/")[-1]
                    fulltext_filepath = f"{project_root}/data/publications/{pmcid}.md"
                    
                    # Ensure directory exists
                    import os
                    os.makedirs(f"{project_root}/data/publications", exist_ok=True)
                    
                    with open(fulltext_filepath, "w", encoding="utf-8") as f:
                        f.write(result['fulltext_markdown'])
                
                fulltext_results.append(result)
                completed_downloads += 1
                progress_manager.update_operation_status("download", completed=completed_downloads)

            # Update fulltext results in database
            fulltext_stats = article_manager.update_fulltext_status(fulltext_results, disease_name)
            progress_manager.add_result("fulltext_download", fulltext_stats)
            progress_manager.update_operation_status("download", status="completed")
        else:
            progress_manager.add_result("fulltext_download", {
                "message": "No PMCIDs available for fulltext download"
            })
    else:
        progress_manager.add_result("fulltext_download", {
            "message": "All articles already have fulltext or no articles need fulltext"
        })

    # Step 5: Extract epidemiological data
    progress_manager.update_progress("Extracting epidemiological data...", increment_completed=True)
    articles_needing_extraction = article_manager.get_articles_needing_extraction(disease_name)

    if articles_needing_extraction:
        pmcids = [str(article.pmcid) for article in articles_needing_extraction if article.pmcid is not None]
        pmids = [str(article.pmid) for article in articles_needing_extraction if article.pmcid is not None]

        if pmcids and pmids:
            # Create pending extraction records
            extraction_manager.create_pending_extraction_records(pmids, pmcids)

            # Calculate total extraction tasks (4 types per article)
            total_extractions = len(pmcids) * 4
            progress_manager.update_operation_status("extraction", total=total_extractions, completed=0, status="running")

            extraction_results = []
            completed_extractions = 0
            all_extraction_stats = {
                'point_prevalence': {'inserted': 0, 'errors': 0},
                'period_prevalence': {'inserted': 0, 'errors': 0},
                'incidence_rate': {'inserted': 0, 'errors': 0},
                'cumulative_incidence': {'inserted': 0, 'errors': 0}
            }

            for result in extract_data_from_article(
                disease_name, pmcids, pmids, max_workers=infra_config.get('extraction_workers', 20)
            ):
                extraction_results.append(result)
                completed_extractions += 1
                progress_manager.update_operation_status("extraction", completed=completed_extractions)

                # Track extraction results for statistics
                if result and result.get('status') == 'SUCCESS':
                    task_type = result.get('task', 'unknown').replace('_extraction', '')
                    if task_type in all_extraction_stats:
                        all_extraction_stats[task_type]['inserted'] += result.get('extracted_datapoints', 0)
                elif result and result.get('status') == 'FAILED':
                    task_type = result.get('task', 'unknown').replace('_extraction', '')
                    if task_type in all_extraction_stats:
                        all_extraction_stats[task_type]['errors'] += 1

            progress_manager.add_result("extraction", all_extraction_stats)
            progress_manager.update_operation_status("extraction", status="completed")
        else:
            progress_manager.add_result("extraction", {
                "message": "No valid PMCIDs found for data extraction"
            })
    else:
        progress_manager.add_result("extraction", {
            "message": "No articles available for data extraction"
        })

    # Step 6: Finalize and display statistics
    progress_manager.update_progress("Finalizing results...", increment_completed=True)
    final_stats = article_manager.get_article_statistics()
    progress_manager.add_result("final_stats", final_stats)

    progress_manager.set_completed()
