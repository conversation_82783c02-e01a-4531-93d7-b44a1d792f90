"""
Data Visualization Dashboard Page

This page provides an interactive dashboard for displaying and filtering
extracted prevalence and incidence data from the epidemiology research system.
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from typing import List, Dict, Any
from datetime import datetime

# Import database utilities
from streamlit_app.utils.database import (
    get_available_diseases,
    get_available_countries,
    get_prevalence_data,
    get_incidence_data,
    get_data_summary_stats,
    clear_data_cache
)

# Import sample data for testing
from streamlit_app.utils.sample_data import (
    generate_sample_prevalence_data,
    generate_sample_incidence_data,
    get_sample_summary_stats,
    get_sample_diseases,
    get_sample_countries
)

def data_dashboard_page():
    """Main data dashboard page."""

    st.title("📊 Data Visualization Dashboard")
    st.markdown("Interactive dashboard for epidemiological data analysis")

    # Check if using sample data
    use_sample_data = st.session_state.dashboard_filters.get("use_sample_data", False)
    if use_sample_data:
        st.info("📊 **Demo Mode**: Displaying sample data for demonstration purposes. Connect to database for real data.")

    # Main dashboard filters (relocated from sidebar)
    setup_filters()

    # Main dashboard content
    col1, col2 = st.columns([2, 1])

    with col1:
        display_data_visualizations()

    with col2:
        display_summary_statistics()
        display_filter_info()

def setup_filters():
    """Setup main area filters for data selection (relocated from sidebar)."""

    st.markdown("### 🔍 Data Filters")

    # Create columns for filter layout
    filter_col1, filter_col2, filter_col3 = st.columns(3)

    # Check if database is connected
    db_connected = st.session_state.get("db_connection_status", {}).get("connected", False)

    with filter_col1:
        # Refresh data button
        if st.button("🔄 Refresh Data", key="dashboard_refresh_data", help="Clear cache and reload data"):
            if db_connected:
                clear_data_cache()
            st.rerun()

    # Use sample data toggle when database is not connected
    use_sample_data = False
    if not db_connected:
        with filter_col2:
            use_sample_data = st.checkbox(
                "📊 Use Sample Data",
                key="dashboard_use_sample_data",
                value=True,
                help="Display sample data for demonstration purposes"
            )
        if not use_sample_data:
            st.warning("No data available without database connection")
            return

    # Disease filter
    if db_connected:
        available_diseases = get_available_diseases()
    else:
        available_diseases = get_sample_diseases()

    # Create filter columns for better layout
    filter_row1_col1, filter_row1_col2 = st.columns(2)
    filter_row2_col1, filter_row2_col2, filter_row2_col3 = st.columns(3)

    with filter_row1_col1:
        selected_diseases = st.multiselect(
            "Select Diseases",
            options=available_diseases,
            default=st.session_state.dashboard_filters["selected_diseases"],
            help="Filter data by specific diseases"
        )

    # Country filter
    if db_connected:
        available_countries = get_available_countries()
    else:
        available_countries = get_sample_countries()

    with filter_row1_col2:
        selected_countries = st.multiselect(
            "Select Countries",
            options=available_countries,
            default=st.session_state.dashboard_filters["selected_countries"],
            help="Filter data by specific countries"
        )

    # Data type filter (simplified for new schema)
    with filter_row2_col1:
        data_type = st.selectbox(
            "Data Type",
            options=["all", "point_prevalence", "cumulative_incidence"],
            index=["all", "point_prevalence", "cumulative_incidence"].index(
                st.session_state.dashboard_filters.get("data_type", "all") if
                st.session_state.dashboard_filters.get("data_type", "all") in ["all", "point_prevalence", "cumulative_incidence"]
                else "all"
            ),
            help="Select which type of data to display"
        )

    # Record limit filter (new)
    with filter_row2_col2:
        record_limit = st.number_input(
            "Record Limit",
            min_value=10,
            max_value=10000,
            value=st.session_state.dashboard_filters.get("record_limit", 1000),
            step=50,
            help="Maximum number of records to display"
        )

    with filter_row2_col3:
        # Use the third column for additional controls if needed
        st.write("")  # Placeholder

    # Update session state
    st.session_state.dashboard_filters.update({
        "selected_diseases": selected_diseases,
        "selected_countries": selected_countries,
        "data_type": data_type,
        "use_sample_data": use_sample_data,
        "record_limit": record_limit
    })

def display_data_visualizations():
    """Display the main data visualizations."""

    filters = st.session_state.dashboard_filters
    use_sample_data = filters.get("use_sample_data", False)

    # Initialize DataFrames with proper typing
    point_prev_df: pd.DataFrame = pd.DataFrame()
    period_prev_df: pd.DataFrame = pd.DataFrame()
    incidence_rate_df: pd.DataFrame = pd.DataFrame()
    cumulative_inc_df: pd.DataFrame = pd.DataFrame()

    # Get filtered data
    if filters["data_type"] in ["all", "prevalence"]:
        if use_sample_data:
            point_prev_df, period_prev_df = generate_sample_prevalence_data()
            # Apply filters to sample data
            if filters["selected_diseases"]:
                disease_filter = point_prev_df['disease_name'].isin(filters["selected_diseases"])
                point_prev_df = pd.DataFrame(point_prev_df[disease_filter])
                disease_filter = period_prev_df['disease_name'].isin(filters["selected_diseases"])
                period_prev_df = pd.DataFrame(period_prev_df[disease_filter])
            if filters["selected_countries"]:
                country_filter = point_prev_df['country'].isin(filters["selected_countries"])
                point_prev_df = pd.DataFrame(point_prev_df[country_filter])
                country_filter = period_prev_df['country'].isin(filters["selected_countries"])
                period_prev_df = pd.DataFrame(period_prev_df[country_filter])
        else:
            selected_diseases = filters["selected_diseases"] if filters["selected_diseases"] else None
            selected_countries = filters["selected_countries"] if filters["selected_countries"] else None
            point_prev_df, period_prev_df = get_prevalence_data(
                diseases=selected_diseases,
                countries=selected_countries
            )

    if filters["data_type"] in ["all", "incidence"]:
        if use_sample_data:
            incidence_rate_df, cumulative_inc_df = generate_sample_incidence_data()
            # Apply filters to sample data
            if filters["selected_diseases"]:
                disease_filter = incidence_rate_df['disease_name'].isin(filters["selected_diseases"])
                incidence_rate_df = pd.DataFrame(incidence_rate_df[disease_filter])
                disease_filter = cumulative_inc_df['disease_name'].isin(filters["selected_diseases"])
                cumulative_inc_df = pd.DataFrame(cumulative_inc_df[disease_filter])
            if filters["selected_countries"]:
                country_filter = incidence_rate_df['country'].isin(filters["selected_countries"])
                incidence_rate_df = pd.DataFrame(incidence_rate_df[country_filter])
                country_filter = cumulative_inc_df['country'].isin(filters["selected_countries"])
                cumulative_inc_df = pd.DataFrame(cumulative_inc_df[country_filter])
        else:
            selected_diseases = filters["selected_diseases"] if filters["selected_diseases"] else None
            selected_countries = filters["selected_countries"] if filters["selected_countries"] else None
            incidence_rate_df, cumulative_inc_df = get_incidence_data(
                diseases=selected_diseases,
                countries=selected_countries
            )
    
    # Display visualizations based on available data
    try:
        if not point_prev_df.empty:
            display_prevalence_charts(point_prev_df, period_prev_df)

        if not incidence_rate_df.empty:
            display_incidence_charts(incidence_rate_df, cumulative_inc_df)

        # Display data tables
        display_data_tables(point_prev_df, period_prev_df, incidence_rate_df, cumulative_inc_df)

        # Show message if no data
        if (point_prev_df.empty and period_prev_df.empty and
            incidence_rate_df.empty and cumulative_inc_df.empty):
            st.info("📊 No data available with current filters. Try adjusting your selection or enable sample data.")

            # Suggest actions
            st.markdown("""
            **Suggestions:**
            - Clear disease/country filters to see all data
            - Enable sample data in the filters for demonstration
            - Check database connection if using real data
            """)

    except Exception as e:
        st.error(f"❌ Error displaying visualizations: {str(e)}")
        st.info("Please try refreshing the page or contact support if the issue persists.")

        # Show debug info in expander
        with st.expander("🔧 Debug Information"):
            st.code(f"Error: {str(e)}")
            st.write("Data shapes:")
            st.write(f"- Point prevalence: {point_prev_df.shape if not point_prev_df.empty else 'Empty'}")
            st.write(f"- Period prevalence: {period_prev_df.shape if not period_prev_df.empty else 'Empty'}")
            st.write(f"- Incidence rate: {incidence_rate_df.shape if not incidence_rate_df.empty else 'Empty'}")
            st.write(f"- Cumulative incidence: {cumulative_inc_df.shape if not cumulative_inc_df.empty else 'Empty'}")

def display_prevalence_charts(point_prev_df: pd.DataFrame, period_prev_df: pd.DataFrame):
    """Display prevalence data visualizations."""
    
    st.markdown("### 📈 Prevalence Data")
    
    # Point prevalence chart
    if not point_prev_df.empty:
        st.markdown("#### Point Prevalence")
        
        # Clean data for visualization - remove rows with NaN values in critical columns
        clean_point_df = point_prev_df.copy()
        
        # Check if required columns exist
        required_cols = ['year', 'point_prevalence_percent', 'disease_name']
        missing_cols = [col for col in required_cols if col not in clean_point_df.columns]
        if missing_cols:
            st.error(f"Missing required columns for visualization: {missing_cols}")
            return
        
        # Fill NaN values in n_population with a default value or remove rows
        if 'n_population' in clean_point_df.columns:
            nan_count = clean_point_df['n_population'].isna().sum()
            if nan_count > 0:
                st.info("⚠️ Some records missing population data - using estimated values for visualization")
                # Fill NaN with median or use a constant size
                median_pop = clean_point_df['n_population'].median()
                if np.isnan(median_pop):
                    fill_value = 1000
                else:
                    fill_value = median_pop
                clean_point_df['n_population'] = clean_point_df['n_population'].fillna(fill_value)
        else:
            # If n_population column doesn't exist, create it with default values
            clean_point_df['n_population'] = 1000
        
        # Ensure no NaN values in required columns
        clean_point_df = clean_point_df.dropna(subset=required_cols)
        
        if not clean_point_df.empty:
            fig = px.scatter(
                clean_point_df,
                x="year",
                y="point_prevalence_percent",
                color="disease_name",
                size="n_population",
                hover_data=["country", "condition", "n_cases"],
                title="Point Prevalence Over Time"
            )
            fig.update_layout(
                xaxis_title="Year",
                yaxis_title="Prevalence (%)",
                height=400
            )
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.warning("No valid data available for point prevalence visualization after cleaning.")
    
    # Period prevalence chart
    if not period_prev_df.empty:
        st.markdown("#### Period Prevalence")
        
        # Clean data for visualization
        clean_period_df = period_prev_df.copy()
        
        # Remove rows with NaN values in critical columns
        required_cols = ['disease_name', 'country', 'period_prevalence_percent']
        clean_period_df = clean_period_df.dropna(subset=required_cols)
        
        if not clean_period_df.empty:
            # Group and aggregate data
            grouped_df = clean_period_df.groupby(["disease_name", "country"])["period_prevalence_percent"].mean().reset_index()
            
            fig = px.bar(
                grouped_df,
                x="country",
                y="period_prevalence_percent",
                color="disease_name",
                title="Average Period Prevalence by Country"
            )
            fig.update_layout(
                xaxis_title="Country",
                yaxis_title="Prevalence (%)",
                height=400
            )
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.warning("No valid data available for period prevalence visualization after cleaning.")

def display_incidence_charts(incidence_rate_df: pd.DataFrame, cumulative_inc_df: pd.DataFrame):
    """Display incidence data visualizations."""

    st.markdown("### 📊 Incidence Data")

    # Incidence rate chart
    if not incidence_rate_df.empty:
        st.markdown("#### Incidence Rate")

        # Clean data for visualization
        clean_rate_df = incidence_rate_df.copy()

        # Remove rows with NaN values in critical columns
        required_cols = ['start_year', 'incidence_rate', 'disease_name']
        clean_rate_df = clean_rate_df.dropna(subset=required_cols)

        if not clean_rate_df.empty:
            # Create tabs for different visualization types
            chart_tab1, chart_tab2, chart_tab3 = st.tabs(["📊 Bar Chart by Disease", "🌍 Country Comparison", "📈 Temporal Overview"])

            with chart_tab1:
                # Bar chart grouped by disease with confidence intervals
                st.markdown("**Incidence Rates by Disease** (with confidence intervals)")

                try:
                    # Prepare data for bar chart - aggregate by disease and country
                    # Check if confidence interval columns exist
                    ci_cols = ['ci_lower', 'ci_upper'] if 'ci_lower' in clean_rate_df.columns and 'ci_upper' in clean_rate_df.columns else []

                    agg_dict = {
                        'incidence_rate': 'mean',
                        'n_cases': 'sum',
                        'person_years': 'sum'
                    }

                    # Add CI columns if they exist
                    if ci_cols:
                        agg_dict.update({'ci_lower': 'mean', 'ci_upper': 'mean'})

                    agg_data = clean_rate_df.groupby(['disease_name', 'country']).agg(agg_dict).reset_index()

                    # Clean aggregated data
                    agg_data = agg_data.dropna(subset=['incidence_rate'])

                    if ci_cols and len(ci_cols) == 2:
                        # Calculate error bars only if CI columns exist and are valid
                        agg_data['error_lower'] = (agg_data['incidence_rate'] - agg_data['ci_lower']).clip(lower=0)
                        agg_data['error_upper'] = (agg_data['ci_upper'] - agg_data['incidence_rate']).clip(lower=0)

                        fig1 = px.bar(
                            agg_data,
                            x="disease_name",
                            y="incidence_rate",
                            color="country",
                            error_y_minus="error_lower",
                            error_y="error_upper",
                            title="Mean Incidence Rate by Disease and Country",
                            hover_data=["n_cases", "person_years"]
                        )
                    else:
                        # Create bar chart without error bars if CI data is not available
                        fig1 = px.bar(
                            agg_data,
                            x="disease_name",
                            y="incidence_rate",
                            color="country",
                            title="Mean Incidence Rate by Disease and Country",
                            hover_data=["n_cases", "person_years"]
                        )

                    fig1.update_layout(
                        xaxis_title="Disease",
                        yaxis_title="Incidence Rate (per 100,000 person-years)",
                        height=500,
                        xaxis_tickangle=-45
                    )
                    st.plotly_chart(fig1, use_container_width=True)

                except Exception as e:
                    st.error(f"Error creating bar chart: {str(e)}")
                    st.info("Please check the data format and try refreshing.")

            with chart_tab2:
                # Horizontal bar chart for country comparison
                st.markdown("**Incidence Rates by Country** (average across all diseases)")

                try:
                    country_agg = clean_rate_df.groupby('country').agg({
                        'incidence_rate': ['mean', 'std', 'count'],
                        'n_cases': 'sum'
                    }).round(2)

                    country_agg.columns = ['mean_rate', 'std_rate', 'study_count', 'total_cases']
                    country_agg = country_agg.reset_index()

                    # Clean the aggregated data
                    country_agg = country_agg.dropna(subset=['mean_rate'])
                    country_agg = country_agg[country_agg['mean_rate'] > 0]  # Remove zero or negative rates

                    if not country_agg.empty:
                        fig2 = px.bar(
                            country_agg,
                            x="mean_rate",
                            y="country",
                            orientation='h',
                            title="Average Incidence Rate by Country",
                            hover_data=["std_rate", "study_count", "total_cases"],
                            color="mean_rate",
                            color_continuous_scale="viridis"
                        )
                        fig2.update_layout(
                            xaxis_title="Mean Incidence Rate (per 100,000 person-years)",
                            yaxis_title="Country",
                            height=400
                        )
                        st.plotly_chart(fig2, use_container_width=True)
                    else:
                        st.warning("No valid data available for country comparison.")

                except Exception as e:
                    st.error(f"Error creating country comparison chart: {str(e)}")
                    st.info("Please check the data format and try refreshing.")

            with chart_tab3:
                # Scatter plot showing temporal patterns with study periods
                st.markdown("**Temporal Distribution of Studies** (study periods and rates)")

                try:
                    # Create a copy for temporal analysis
                    temporal_df = clean_rate_df.copy()

                    # Ensure we have the required columns
                    required_temporal_cols = ['start_year', 'end_year', 'incidence_rate', 'disease_name', 'country', 'n_cases']
                    missing_cols = [col for col in required_temporal_cols if col not in temporal_df.columns]

                    if missing_cols:
                        st.error(f"Missing required columns for temporal analysis: {missing_cols}")
                    else:
                        # Clean temporal data
                        temporal_df = temporal_df.dropna(subset=required_temporal_cols)

                        # Create mid-year for better temporal representation
                        temporal_df['mid_year'] = (temporal_df['start_year'] + temporal_df['end_year']) / 2
                        temporal_df['study_duration'] = temporal_df['end_year'] - temporal_df['start_year']

                        # Clean and validate size column (n_cases)
                        temporal_df['n_cases_clean'] = pd.to_numeric(temporal_df['n_cases'], errors='coerce')
                        temporal_df = temporal_df.dropna(subset=['n_cases_clean'])
                        temporal_df = temporal_df[temporal_df['n_cases_clean'] > 0]  # Remove zero or negative cases

                        # Ensure we have valid data for plotting
                        if not temporal_df.empty and len(temporal_df) > 0:
                            # Create scatter plot with cleaned data
                            fig3 = px.scatter(
                                temporal_df,
                                x="mid_year",
                                y="incidence_rate",
                                color="disease_name",
                                size="n_cases_clean",
                                symbol="country",
                                hover_data=["start_year", "end_year", "study_duration", "person_years"] if 'person_years' in temporal_df.columns else ["start_year", "end_year", "study_duration"],
                                title="Incidence Rate Studies Over Time",
                                size_max=20  # Limit maximum marker size
                            )
                            fig3.update_layout(
                                xaxis_title="Study Mid-Year",
                                yaxis_title="Incidence Rate (per 100,000 person-years)",
                                height=500
                            )
                            st.plotly_chart(fig3, use_container_width=True)

                            # Add summary statistics
                            st.markdown("**Study Period Summary:**")
                            col1, col2, col3, col4 = st.columns(4)
                            with col1:
                                st.metric("Total Studies", len(temporal_df))
                            with col2:
                                avg_duration = temporal_df['study_duration'].mean()
                                st.metric("Avg Study Duration", f"{avg_duration:.1f} years" if not pd.isna(avg_duration) else "N/A")
                            with col3:
                                min_year = temporal_df['start_year'].min()
                                max_year = temporal_df['end_year'].max()
                                if not pd.isna(min_year) and not pd.isna(max_year):
                                    st.metric("Year Range", f"{int(min_year)}-{int(max_year)}")
                                else:
                                    st.metric("Year Range", "N/A")
                            with col4:
                                total_cases = temporal_df['n_cases_clean'].sum()
                                st.metric("Total Cases", f"{int(total_cases):,}" if not pd.isna(total_cases) else "N/A")
                        else:
                            st.warning("No valid data available for temporal analysis after cleaning.")

                except Exception as e:
                    st.error(f"Error creating temporal overview: {str(e)}")
                    st.info("Please check the data format and try refreshing.")
                    # Show debug info
                    with st.expander("Debug Information"):
                        st.write("Available columns:", list(clean_rate_df.columns))
                        st.write("Data shape:", clean_rate_df.shape)
                        if not clean_rate_df.empty:
                            st.write("Sample data:")
                            st.dataframe(clean_rate_df.head())
        else:
            st.warning("No valid data available for incidence rate visualization after cleaning.")
    
    # Cumulative incidence chart
    if not cumulative_inc_df.empty:
        st.markdown("#### Cumulative Incidence")
        
        # Clean data for visualization
        clean_cumulative_df = cumulative_inc_df.copy()
        
        # Remove rows with NaN values in critical columns
        required_cols = ['disease_name', 'cumulative_incidence_percent']
        clean_cumulative_df = clean_cumulative_df.dropna(subset=required_cols)
        
        if not clean_cumulative_df.empty:
            fig = px.box(
                clean_cumulative_df,
                x="disease_name",
                y="cumulative_incidence_percent",
                color="country",
                title="Cumulative Incidence Distribution"
            )
            fig.update_layout(
                xaxis_title="Disease",
                yaxis_title="Cumulative Incidence (%)",
                height=400
            )
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.warning("No valid data available for cumulative incidence visualization after cleaning.")

def display_data_tables(point_prev_df: pd.DataFrame, period_prev_df: pd.DataFrame, 
                       incidence_rate_df: pd.DataFrame, cumulative_inc_df: pd.DataFrame):
    """Display data tables with filtering and sorting."""
    
    st.markdown("### 📋 Data Tables")
    
    # Create tabs for different data types
    tabs = []
    tab_data = []
    
    if not point_prev_df.empty:
        tabs.append("Point Prevalence")
        tab_data.append(point_prev_df)
    
    if not period_prev_df.empty:
        tabs.append("Period Prevalence")
        tab_data.append(period_prev_df)
    
    if not incidence_rate_df.empty:
        tabs.append("Incidence Rate")
        tab_data.append(incidence_rate_df)
    
    if not cumulative_inc_df.empty:
        tabs.append("Cumulative Incidence")
        tab_data.append(cumulative_inc_df)
    
    if tabs:
        tab_objects = st.tabs(tabs)
        
        for i, (tab, df) in enumerate(zip(tab_objects, tab_data)):
            with tab:
                # Display summary
                st.write(f"**Total Records:** {len(df)}")
                
                # Display table with pagination
                if len(df) > 100:
                    st.info(f"Showing first 100 of {len(df)} records. Use filters to narrow results.")
                    display_df = df.head(100)
                else:
                    display_df = df
                
                st.dataframe(
                    display_df,
                    use_container_width=True,
                    hide_index=True
                )
                
                # Download button
                csv = df.to_csv(index=False)
                st.download_button(
                    label=f"📥 Download {tabs[i]} Data (CSV)",
                    data=csv,
                    file_name=f"{tabs[i].lower().replace(' ', '_')}_data.csv",
                    mime="text/csv"
                )

def display_summary_statistics():
    """Display summary statistics."""

    st.markdown("### 📊 Summary Statistics")

    use_sample_data = st.session_state.dashboard_filters.get("use_sample_data", False)

    if use_sample_data:
        stats = get_sample_summary_stats()
        st.info("📊 Displaying sample data statistics")
    else:
        stats = get_data_summary_stats()

    if stats:
        st.metric("Total Articles", stats.get("total_articles", 0))
        st.metric("Point Prevalence Records", stats.get("point_prevalence", 0))
        st.metric("Period Prevalence Records", stats.get("period_prevalence", 0))
        st.metric("Incidence Rate Records", stats.get("incidence_rate", 0))
        st.metric("Cumulative Incidence Records", stats.get("cumulative_incidence", 0))
    else:
        st.info("No data available")

def display_filter_info():
    """Display current filter information."""
    
    st.markdown("### 🔍 Current Filters")
    
    filters = st.session_state.dashboard_filters
    
    if filters["selected_diseases"]:
        st.write("**Diseases:**")
        for disease in filters["selected_diseases"]:
            st.write(f"• {disease}")
    else:
        st.write("**Diseases:** All")
    
    if filters["selected_countries"]:
        st.write("**Countries:**")
        for country in filters["selected_countries"]:
            st.write(f"• {country}")
    else:
        st.write("**Countries:** All")
    
    st.write(f"**Data Type:** {filters['data_type'].title()}")
    
    # Last updated info
    st.markdown("---")
    st.caption(f"Last updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
