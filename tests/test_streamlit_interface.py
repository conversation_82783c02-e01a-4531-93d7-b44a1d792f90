"""
Unit tests for the updated Streamlit interface.
"""

import unittest
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'streamlit_app', 'pages'))

from research_interface import get_country_options, extract_country_code


class TestStreamlitInterface(unittest.TestCase):
    """Test cases for the updated Streamlit interface."""

    def test_get_country_options(self):
        """Test that country options are generated correctly."""
        options = get_country_options()
        
        # Should have at least some countries
        self.assertGreater(len(options), 10)
        
        # Should start with empty option
        self.assertEqual(options[0], "")
        
        # Should contain common countries in expected format
        us_found = False
        uk_found = False
        for option in options:
            if "United States (US)" in option:
                us_found = True
            if "United Kingdom (GB)" in option:
                uk_found = True
        
        self.assertTrue(us_found or uk_found, "Should contain at least one major country")

    def test_extract_country_code_valid(self):
        """Test extracting country code from valid selections."""
        # Test standard format
        self.assertEqual(extract_country_code("United States (US)"), "US")
        self.assertEqual(extract_country_code("United Kingdom (GB)"), "GB")
        self.assertEqual(extract_country_code("Canada (CA)"), "CA")
        
        # Test with longer country names
        self.assertEqual(extract_country_code("United Arab Emirates (AE)"), "AE")

    def test_extract_country_code_invalid(self):
        """Test extracting country code from invalid selections."""
        # Test empty string
        self.assertEqual(extract_country_code(""), "")
        
        # Test None
        self.assertEqual(extract_country_code(None), "")
        
        # Test malformed strings
        self.assertEqual(extract_country_code("United States"), "United States")
        self.assertEqual(extract_country_code("No parentheses"), "No parentheses")

    def test_extract_country_code_edge_cases(self):
        """Test edge cases for country code extraction."""
        # Test multiple parentheses
        self.assertEqual(extract_country_code("Country (with) (US)"), "US")
        
        # Test empty parentheses
        self.assertEqual(extract_country_code("Country ()"), "")
        
        # Test only parentheses
        self.assertEqual(extract_country_code("(US)"), "US")

    def test_country_options_format(self):
        """Test that country options follow the expected format."""
        options = get_country_options()
        
        # Skip empty option
        for option in options[1:10]:  # Test first 10 non-empty options
            if option:  # Skip any empty options
                # Should contain parentheses
                self.assertIn("(", option)
                self.assertIn(")", option)
                
                # Should end with parentheses containing 2-3 characters
                parts = option.split("(")
                if len(parts) > 1:
                    code_part = parts[-1].replace(")", "")
                    self.assertGreaterEqual(len(code_part), 2)
                    self.assertLessEqual(len(code_part), 3)

    def test_country_options_sorted(self):
        """Test that country options are sorted alphabetically."""
        options = get_country_options()
        
        # Remove empty option for sorting test
        non_empty_options = [opt for opt in options if opt]
        
        # Should be sorted
        self.assertEqual(non_empty_options, sorted(non_empty_options))

    def test_country_options_no_duplicates(self):
        """Test that there are no duplicate country options."""
        options = get_country_options()
        
        # Should have no duplicates
        self.assertEqual(len(options), len(set(options)))

    def test_fallback_countries(self):
        """Test that fallback countries are available if pycountry fails."""
        # This test ensures the fallback mechanism works
        # We can't easily mock pycountry failure, but we can check the fallback list
        fallback_countries = [
            "", "United States (US)", "United Kingdom (GB)", "Canada (CA)", 
            "Australia (AU)", "Germany (DE)", "France (FR)", "Japan (JP)", 
            "China (CN)", "India (IN)"
        ]
        
        for country in fallback_countries:
            if country:  # Skip empty string
                code = extract_country_code(country)
                self.assertGreater(len(code), 0)

    def test_common_countries_present(self):
        """Test that common countries are present in the options."""
        options = get_country_options()
        options_str = " ".join(options)
        
        # Common countries that should be present
        common_countries = ["US", "GB", "CA", "AU", "DE", "FR", "JP", "CN", "IN"]
        
        found_countries = 0
        for country_code in common_countries:
            if f"({country_code})" in options_str:
                found_countries += 1
        
        # Should find at least half of the common countries
        self.assertGreaterEqual(found_countries, len(common_countries) // 2)

    def test_extract_country_code_consistency(self):
        """Test that extract_country_code is consistent with get_country_options."""
        options = get_country_options()
        
        # Test a few options to ensure consistency
        for option in options[1:6]:  # Test first 5 non-empty options
            if option and "(" in option and ")" in option:
                extracted_code = extract_country_code(option)
                
                # The extracted code should be present in the original option
                self.assertIn(f"({extracted_code})", option)


if __name__ == '__main__':
    unittest.main()
