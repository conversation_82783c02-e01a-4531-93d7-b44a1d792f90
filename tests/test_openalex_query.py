"""
Unit tests for the enhanced OpenAlex query construction.
"""

import unittest
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from handlers.openalex_handler import OpenAlexClient


class TestOpenAlexQueryConstruction(unittest.TestCase):
    """Test cases for the enhanced OpenAlex query construction."""

    def setUp(self):
        """Set up test fixtures."""
        self.client = OpenAlexClient()

    def test_basic_query_construction(self):
        """Test basic query construction without additional parameters."""
        disease_keywords = ["diabetes", "type 2 diabetes"]
        query = self.client.construct_search_query(disease_keywords)
        
        # Should contain disease terms
        self.assertIn("diabetes", query)
        self.assertIn("type 2 diabetes", query)
        
        # Should contain epidemiological terms
        self.assertIn("prevalence", query)
        self.assertIn("incidence", query)
        self.assertIn("epidemiology", query)
        
        # Should contain exclusion terms
        self.assertIn("NOT", query)
        self.assertIn("systematic review", query)
        self.assertIn("meta-analysis", query)

    def test_query_with_country_parameter(self):
        """Test query construction with country parameter."""
        disease_keywords = ["diabetes"]
        query = self.client.construct_search_query(disease_keywords, country="US")
        
        # Should contain country-related terms
        self.assertIn("US", query)
        self.assertIn("United States", query)

    def test_query_with_time_period_parameter(self):
        """Test query construction with time period parameter."""
        disease_keywords = ["diabetes"]
        query = self.client.construct_search_query(disease_keywords, time_period="2020-2022")
        
        # Should contain year terms
        self.assertIn("2020", query)
        self.assertIn("2021", query)
        self.assertIn("2022", query)

    def test_query_with_both_parameters(self):
        """Test query construction with both country and time period."""
        disease_keywords = ["diabetes"]
        query = self.client.construct_search_query(
            disease_keywords, 
            country="CA", 
            time_period="2019-2021"
        )
        
        # Should contain disease terms
        self.assertIn("diabetes", query)
        
        # Should contain country terms
        self.assertIn("CA", query)
        self.assertIn("Canada", query)
        
        # Should contain year terms
        self.assertIn("2019", query)
        self.assertIn("2020", query)
        self.assertIn("2021", query)
        
        # Should contain epidemiological terms
        self.assertIn("prevalence", query)
        
        # Should contain exclusions
        self.assertIn("NOT", query)

    def test_invalid_time_period_handling(self):
        """Test handling of invalid time period formats."""
        disease_keywords = ["diabetes"]
        
        # Invalid format should not crash
        query = self.client.construct_search_query(disease_keywords, time_period="invalid")
        self.assertIn("diabetes", query)
        
        # Empty time period should not crash
        query = self.client.construct_search_query(disease_keywords, time_period="")
        self.assertIn("diabetes", query)

    def test_invalid_country_handling(self):
        """Test handling of invalid country codes."""
        disease_keywords = ["diabetes"]
        
        # Invalid country code should not crash
        query = self.client.construct_search_query(disease_keywords, country="XX")
        self.assertIn("diabetes", query)
        self.assertIn("XX", query)  # Should still include the provided code

    def test_enhanced_exclusion_terms(self):
        """Test that enhanced exclusion terms are included."""
        disease_keywords = ["diabetes"]
        query = self.client.construct_search_query(disease_keywords)
        
        # Should include enhanced exclusion terms
        exclusion_terms = [
            "case report", "clinical trial", "randomized controlled trial",
            "genetic association", "biomarker", "in silico", "computational model"
        ]
        
        for term in exclusion_terms:
            self.assertIn(term, query)

    def test_enhanced_epidemiological_terms(self):
        """Test that enhanced epidemiological terms are included."""
        disease_keywords = ["diabetes"]
        query = self.client.construct_search_query(disease_keywords)
        
        # Should include enhanced epidemiological terms
        epi_terms = [
            "surveillance", "population-based", "national survey",
            "disease burden", "public health", "registry-based"
        ]
        
        for term in epi_terms:
            self.assertIn(term, query)

    def test_query_structure(self):
        """Test the overall structure of the constructed query."""
        disease_keywords = ["diabetes"]
        query = self.client.construct_search_query(
            disease_keywords, 
            country="US", 
            time_period="2020-2021"
        )
        
        # Should have proper Boolean structure
        self.assertIn(" AND ", query)
        self.assertIn(" NOT ", query)
        self.assertIn("(", query)
        self.assertIn(")", query)
        
        # Should start with disease terms
        self.assertTrue(query.startswith('("diabetes")'))

    def test_long_time_period_limitation(self):
        """Test that very long time periods are limited to prevent overly long queries."""
        disease_keywords = ["diabetes"]
        query = self.client.construct_search_query(disease_keywords, time_period="2000-2030")
        
        # Should contain some years but not all 30 years (limited to 10)
        self.assertIn("2000", query)
        self.assertIn("2009", query)  # Should include up to 10 years
        # Should not include later years due to limitation
        self.assertNotIn("2025", query)

    def test_multiple_disease_keywords(self):
        """Test query construction with multiple disease keywords."""
        disease_keywords = ["diabetes", "type 2 diabetes", "diabetes mellitus"]
        query = self.client.construct_search_query(disease_keywords)
        
        # Should contain all disease keywords
        for keyword in disease_keywords:
            self.assertIn(keyword, query)
        
        # Should use OR logic for disease keywords
        self.assertIn('" OR "', query)


if __name__ == '__main__':
    unittest.main()
