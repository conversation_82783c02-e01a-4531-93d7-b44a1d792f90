"""
Unit tests for the abstract screening agent with stricter filtering criteria.
"""

import unittest
from unittest.mock import Mock, patch
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from agents.article_screening import (
    DiseaseAbstractScreeningAgent,
    screen_abstract_for_disease_epidemiology,
    DiseaseEpidemiologyScreeningResult
)


class TestAbstractScreeningAgent(unittest.TestCase):
    """Test cases for the abstract screening agent with strict filtering."""

    def setUp(self):
        """Set up test fixtures."""
        self.agent = DiseaseAbstractScreeningAgent()

    @patch('agents.article_screening.Agent')
    def test_country_level_study_accepted(self, mock_agent_class):
        """Test that country-level studies are accepted."""
        # Mock the agent response
        mock_agent = Mock()
        mock_response = Mock()
        mock_response.content = DiseaseEpidemiologyScreeningResult(is_relevant=True)
        mock_agent.run.return_value = mock_response
        mock_agent_class.return_value = mock_agent
        
        # Create new agent instance to use mocked Agent
        agent = DiseaseAbstractScreeningAgent()
        
        title = "National prevalence of diabetes in the United States: a population-based study"
        abstract = """
        Background: We conducted a national cross-sectional study to estimate the prevalence of diabetes in the United States.
        Methods: We analyzed data from a nationally representative sample of adults aged 18+ years.
        Results: The overall prevalence of diabetes was 8.5% (95% CI: 8.1-8.9%).
        Conclusion: This study provides country-level prevalence estimates for diabetes.
        """
        
        result = agent.screen_abstract("diabetes", title, abstract, "2020-2025")
        self.assertTrue(result["is_relevant"])

    @patch('agents.article_screening.Agent')
    def test_regional_study_rejected(self, mock_agent_class):
        """Test that regional/city-level studies are rejected."""
        # Mock the agent response
        mock_agent = Mock()
        mock_response = Mock()
        mock_response.content = DiseaseEpidemiologyScreeningResult(is_relevant=False)
        mock_agent.run.return_value = mock_response
        mock_agent_class.return_value = mock_agent
        
        agent = DiseaseAbstractScreeningAgent()
        
        title = "Prevalence of diabetes in New York City: a regional analysis"
        abstract = """
        Background: We studied diabetes prevalence in New York City.
        Methods: We analyzed data from NYC health records.
        Results: The prevalence was 9.2% in Manhattan and 10.1% in Brooklyn.
        Conclusion: Regional variations exist within NYC.
        """
        
        result = agent.screen_abstract("diabetes", title, abstract, "2020-2025")
        self.assertFalse(result["is_relevant"])

    @patch('agents.article_screening.Agent')
    def test_multi_disease_study_rejected(self, mock_agent_class):
        """Test that multi-disease studies are rejected."""
        mock_agent = Mock()
        mock_response = Mock()
        mock_response.content = DiseaseEpidemiologyScreeningResult(is_relevant=False)
        mock_agent.run.return_value = mock_response
        mock_agent_class.return_value = mock_agent
        
        agent = DiseaseAbstractScreeningAgent()
        
        title = "Prevalence of diabetes and hypertension in the United States"
        abstract = """
        Background: We examined the prevalence of diabetes and hypertension nationally.
        Methods: National survey data was analyzed for both conditions.
        Results: Diabetes prevalence was 8.5% and hypertension was 45.4%.
        Conclusion: Both conditions are common in the US population.
        """
        
        result = agent.screen_abstract("diabetes", title, abstract, "2020-2025")
        self.assertFalse(result["is_relevant"])

    @patch('agents.article_screening.Agent')
    def test_ethnicity_stratified_study_rejected(self, mock_agent_class):
        """Test that studies with ethnicity stratification are rejected."""
        mock_agent = Mock()
        mock_response = Mock()
        mock_response.content = DiseaseEpidemiologyScreeningResult(is_relevant=False)
        mock_agent.run.return_value = mock_response
        mock_agent_class.return_value = mock_agent
        
        agent = DiseaseAbstractScreeningAgent()
        
        title = "Diabetes prevalence by race and ethnicity in the United States"
        abstract = """
        Background: We examined diabetes prevalence across racial/ethnic groups.
        Methods: National data was stratified by race and ethnicity.
        Results: Prevalence was 7.5% in whites, 11.7% in blacks, and 12.8% in Hispanics.
        Conclusion: Significant ethnic disparities exist in diabetes prevalence.
        """
        
        result = agent.screen_abstract("diabetes", title, abstract, "2020-2025")
        self.assertFalse(result["is_relevant"])

    @patch('agents.article_screening.Agent')
    def test_age_stratified_study_rejected(self, mock_agent_class):
        """Test that studies with age stratification are rejected."""
        mock_agent = Mock()
        mock_response = Mock()
        mock_response.content = DiseaseEpidemiologyScreeningResult(is_relevant=False)
        mock_agent.run.return_value = mock_response
        mock_agent_class.return_value = mock_agent
        
        agent = DiseaseAbstractScreeningAgent()
        
        title = "Age-specific diabetes prevalence in the United States"
        abstract = """
        Background: We examined diabetes prevalence across age groups.
        Methods: National data was analyzed by age categories.
        Results: Prevalence was 2.1% in 18-44 years, 15.3% in 45-64 years, and 26.8% in 65+ years.
        Conclusion: Diabetes prevalence increases significantly with age.
        """
        
        result = agent.screen_abstract("diabetes", title, abstract, "2020-2025")
        self.assertFalse(result["is_relevant"])

    @patch('agents.article_screening.Agent')
    def test_gender_stratified_study_accepted(self, mock_agent_class):
        """Test that studies with gender stratification are accepted."""
        mock_agent = Mock()
        mock_response = Mock()
        mock_response.content = DiseaseEpidemiologyScreeningResult(is_relevant=True)
        mock_agent.run.return_value = mock_response
        mock_agent_class.return_value = mock_agent
        
        agent = DiseaseAbstractScreeningAgent()
        
        title = "Gender differences in diabetes prevalence in the United States"
        abstract = """
        Background: We examined national diabetes prevalence by gender.
        Methods: National survey data was analyzed for males and females.
        Results: Prevalence was 9.1% in males and 7.9% in females.
        Conclusion: Males have slightly higher diabetes prevalence nationally.
        """
        
        result = agent.screen_abstract("diabetes", title, abstract, "2020-2025")
        self.assertTrue(result["is_relevant"])

    def test_empty_inputs_rejected(self):
        """Test that empty inputs are rejected."""
        result = self.agent.screen_abstract("", "title", "abstract")
        self.assertFalse(result["is_relevant"])
        
        result = self.agent.screen_abstract("diabetes", "", "")
        self.assertFalse(result["is_relevant"])
        
        result = self.agent.screen_abstract("diabetes", "title", "")
        self.assertFalse(result["is_relevant"])

    def test_convenience_function_with_time_period(self):
        """Test the convenience function with time period parameter."""
        with patch('agents.article_screening.DiseaseAbstractScreeningAgent') as mock_agent_class:
            mock_agent = Mock()
            mock_agent.screen_abstract.return_value = {"is_relevant": True}
            mock_agent_class.return_value = mock_agent
            
            result = screen_abstract_for_disease_epidemiology(
                "diabetes", "title", "abstract", "2020-2025"
            )
            
            # Verify the agent was called with time_period
            mock_agent.screen_abstract.assert_called_once_with(
                "diabetes", "title", "abstract", "2020-2025"
            )
            self.assertTrue(result["is_relevant"])


if __name__ == '__main__':
    unittest.main()
