"""
Unit tests for the simplified data extraction agents.
"""

import unittest
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from agents.data_extraction import extract_data_from_article
from helpers.epidata_models import PointPrevalenceRecord, CumulativeIncidenceRecord


class TestSimplifiedDataExtraction(unittest.TestCase):
    """Test cases for the simplified data extraction system."""

    def test_simplified_point_prevalence_model(self):
        """Test that the simplified PointPrevalenceRecord model works correctly."""
        # Test with minimal required fields
        record = PointPrevalenceRecord(
            condition="Type 2 diabetes",
            point_prevalence_percent=8.5,
            year=2020
        )
        
        self.assertEqual(record.condition, "Type 2 diabetes")
        self.assertEqual(record.point_prevalence_percent, 8.5)
        self.assertEqual(record.year, 2020)
        self.assertEqual(record.gender, "both")  # Default value
        self.assertIsNone(record.country)  # Optional field

    def test_simplified_point_prevalence_with_demographics(self):
        """Test PointPrevalenceRecord with demographic information."""
        record = PointPrevalenceRecord(
            condition="Hypertension",
            point_prevalence_percent=25.3,
            year=2021,
            country="US",
            gender="female"
        )
        
        self.assertEqual(record.condition, "Hypertension")
        self.assertEqual(record.point_prevalence_percent, 25.3)
        self.assertEqual(record.year, 2021)
        self.assertEqual(record.country, "US")
        self.assertEqual(record.gender, "female")

    def test_simplified_cumulative_incidence_model(self):
        """Test that the simplified CumulativeIncidenceRecord model works correctly."""
        # Test with minimal required fields
        record = CumulativeIncidenceRecord(
            condition="Type 1 diabetes",
            cumulative_incidence_percent=0.5,
            start_year=2018,
            end_year=2020
        )
        
        self.assertEqual(record.condition, "Type 1 diabetes")
        self.assertEqual(record.cumulative_incidence_percent, 0.5)
        self.assertEqual(record.start_year, 2018)
        self.assertEqual(record.end_year, 2020)
        self.assertEqual(record.gender, "both")  # Default value
        self.assertIsNone(record.country)  # Optional field

    def test_simplified_cumulative_incidence_with_demographics(self):
        """Test CumulativeIncidenceRecord with demographic information."""
        record = CumulativeIncidenceRecord(
            condition="Stroke",
            cumulative_incidence_percent=2.1,
            start_year=2015,
            end_year=2020,
            country="CA",
            gender="male"
        )
        
        self.assertEqual(record.condition, "Stroke")
        self.assertEqual(record.cumulative_incidence_percent, 2.1)
        self.assertEqual(record.start_year, 2015)
        self.assertEqual(record.end_year, 2020)
        self.assertEqual(record.country, "CA")
        self.assertEqual(record.gender, "male")

    def test_point_prevalence_validation(self):
        """Test validation of PointPrevalenceRecord fields."""
        # Test percentage range validation
        with self.assertRaises(ValueError):
            PointPrevalenceRecord(
                condition="Diabetes",
                point_prevalence_percent=150.0,  # Invalid: > 100
                year=2020
            )
        
        with self.assertRaises(ValueError):
            PointPrevalenceRecord(
                condition="Diabetes",
                point_prevalence_percent=-5.0,  # Invalid: < 0
                year=2020
            )

    def test_cumulative_incidence_validation(self):
        """Test validation of CumulativeIncidenceRecord fields."""
        # Test percentage range validation
        with self.assertRaises(ValueError):
            CumulativeIncidenceRecord(
                condition="Cancer",
                cumulative_incidence_percent=150.0,  # Invalid: > 100
                start_year=2018,
                end_year=2020
            )
        
        with self.assertRaises(ValueError):
            CumulativeIncidenceRecord(
                condition="Cancer",
                cumulative_incidence_percent=-1.0,  # Invalid: < 0
                start_year=2018,
                end_year=2020
            )

    def test_year_validation(self):
        """Test year validation for both models."""
        # Test invalid year for PointPrevalenceRecord
        with self.assertRaises(ValueError):
            PointPrevalenceRecord(
                condition="Diabetes",
                point_prevalence_percent=8.5,
                year=1700  # Invalid: < 1800
            )
        
        # Test invalid years for CumulativeIncidenceRecord
        with self.assertRaises(ValueError):
            CumulativeIncidenceRecord(
                condition="Cancer",
                cumulative_incidence_percent=2.0,
                start_year=1700,  # Invalid: < 1800
                end_year=2020
            )

    def test_gender_validation(self):
        """Test gender field validation."""
        # Valid gender values
        valid_genders = ["male", "female", "both", "unknown"]
        
        for gender in valid_genders:
            record = PointPrevalenceRecord(
                condition="Diabetes",
                point_prevalence_percent=8.5,
                year=2020,
                gender=gender
            )
            self.assertEqual(record.gender, gender)

    def test_data_extraction_function_simplified(self):
        """Test that the data extraction function works with simplified agents."""
        # This is a basic test to ensure the function can be called
        # In a real scenario, this would require actual PMC articles
        disease_name = "Diabetes"
        pmcids = []  # Empty list for testing
        pmids = []   # Empty list for testing
        
        # Should not raise an exception with empty lists
        results = list(extract_data_from_article(disease_name, pmcids, pmids))
        self.assertEqual(len(results), 0)

    def test_removed_fields_not_accessible(self):
        """Test that removed fields are no longer accessible in simplified models."""
        # Test PointPrevalenceRecord
        record = PointPrevalenceRecord(
            condition="Diabetes",
            point_prevalence_percent=8.5,
            year=2020
        )
        
        # These fields should not exist in the simplified model
        with self.assertRaises(AttributeError):
            _ = record.ci_lower_percent
        
        with self.assertRaises(AttributeError):
            _ = record.ci_upper_percent
        
        with self.assertRaises(AttributeError):
            _ = record.n_cases
        
        with self.assertRaises(AttributeError):
            _ = record.n_population
        
        with self.assertRaises(AttributeError):
            _ = record.age_group
        
        with self.assertRaises(AttributeError):
            _ = record.ethnicity

    def test_cumulative_incidence_removed_fields(self):
        """Test that removed fields are no longer accessible in CumulativeIncidenceRecord."""
        record = CumulativeIncidenceRecord(
            condition="Cancer",
            cumulative_incidence_percent=2.0,
            start_year=2018,
            end_year=2020
        )
        
        # These fields should not exist in the simplified model
        with self.assertRaises(AttributeError):
            _ = record.ci_lower_percent
        
        with self.assertRaises(AttributeError):
            _ = record.ci_upper_percent
        
        with self.assertRaises(AttributeError):
            _ = record.n_new_cases
        
        with self.assertRaises(AttributeError):
            _ = record.n_at_risk_start
        
        with self.assertRaises(AttributeError):
            _ = record.age_group
        
        with self.assertRaises(AttributeError):
            _ = record.ethnicity


if __name__ == '__main__':
    unittest.main()
